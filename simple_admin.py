#!/usr/bin/env python3
"""
Simple Admin Panel for Travela Tourism Website
This creates a simple web interface to view your backend data
"""

from flask import Flask, render_template_string
import sqlite3
import os

app = Flask(__name__)

@app.route('/')
def admin_dashboard():
    """Simple admin dashboard"""
    
    if not os.path.exists('tourism.db'):
        return """
        <h1>❌ Database Not Found</h1>
        <p>Please run the main Flask app first to create the database:</p>
        <code>python app.py</code>
        """
    
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    try:
        # Get statistics
        cursor.execute("SELECT COUNT(*) FROM contact")
        total_contacts = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM booking")
        total_bookings = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM newsletter")
        total_subscribers = cursor.fetchone()[0]
        
        # Get recent contacts
        cursor.execute("""
            SELECT first_name, last_name, email, subject, created_at 
            FROM contact 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        recent_contacts = cursor.fetchall()
        
        # Get recent bookings
        cursor.execute("""
            SELECT booking_id, first_name, last_name, destination, created_at 
            FROM booking 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        recent_bookings = cursor.fetchall()
        
    except sqlite3.Error as e:
        return f"<h1>❌ Database Error</h1><p>{e}</p>"
    finally:
        conn.close()
    
    html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Travela Admin Panel</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            .dashboard-card {{ border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }}
            .stat-icon {{ font-size: 2.5rem; opacity: 0.8; }}
        </style>
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-dark bg-primary">
            <div class="container">
                <span class="navbar-brand mb-0 h1">
                    <i class="fa fa-plane me-2"></i>Travela Admin Panel
                </span>
                <a href="http://localhost:8000" class="btn btn-outline-light">
                    <i class="fa fa-home me-2"></i>View Website
                </a>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-envelope stat-icon text-primary"></i>
                            <h3 class="mt-2">{total_contacts}</h3>
                            <p class="text-muted">Total Contacts</p>
                            <a href="/contacts" class="btn btn-outline-primary btn-sm">View All</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-calendar-check stat-icon text-success"></i>
                            <h3 class="mt-2">{total_bookings}</h3>
                            <p class="text-muted">Total Bookings</p>
                            <a href="/bookings" class="btn btn-outline-success btn-sm">View All</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-users stat-icon text-info"></i>
                            <h3 class="mt-2">{total_subscribers}</h3>
                            <p class="text-muted">Newsletter Subscribers</p>
                            <a href="/newsletter" class="btn btn-outline-info btn-sm">View All</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <!-- Recent Contacts -->
                <div class="col-md-6">
                    <div class="card dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-envelope me-2"></i>Recent Contacts
                            </h5>
                        </div>
                        <div class="card-body">
    """
    
    if recent_contacts:
        for contact in recent_contacts:
            html += f"""
                            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                <div>
                                    <strong>{contact[0]} {contact[1]}</strong><br>
                                    <small class="text-muted">{contact[3]}</small><br>
                                    <small class="text-primary">{contact[2]}</small>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{contact[4]}</small>
                                </div>
                            </div>
            """
    else:
        html += '<p class="text-muted">No contacts yet.</p>'
    
    html += """
                            <div class="text-center mt-3">
                                <a href="/contacts" class="btn btn-outline-primary btn-sm">View All Contacts</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="col-md-6">
                    <div class="card dashboard-card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-calendar-check me-2"></i>Recent Bookings
                            </h5>
                        </div>
                        <div class="card-body">
    """
    
    if recent_bookings:
        for booking in recent_bookings:
            html += f"""
                            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                <div>
                                    <strong>{booking[1]} {booking[2]}</strong><br>
                                    <small class="text-muted">{booking[3]}</small><br>
                                    <small class="text-primary">{booking[0]}</small>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{booking[4]}</small>
                                </div>
                            </div>
            """
    else:
        html += '<p class="text-muted">No bookings yet.</p>'
    
    html += """
                            <div class="text-center mt-3">
                                <a href="/bookings" class="btn btn-outline-success btn-sm">View All Bookings</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-info-circle me-2"></i>How to View Your Data
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>📊 Web Interface (This Page)</h6>
                                    <ul>
                                        <li>Dashboard: <code>http://localhost:3000</code></li>
                                        <li>All Contacts: <code>http://localhost:3000/contacts</code></li>
                                        <li>All Bookings: <code>http://localhost:3000/bookings</code></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>💻 Command Line</h6>
                                    <ul>
                                        <li>Run: <code>python view_data.py</code></li>
                                        <li>Interactive data viewer</li>
                                        <li>Detailed information display</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="alert alert-warning mt-3">
                                <strong>📧 Email Setup:</strong> Configure your email in <code>.env</code> file to receive notifications when forms are submitted.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    return html

@app.route('/contacts')
def view_contacts():
    """View all contacts"""
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT first_name, last_name, email, phone, inquiry_type, subject, message, created_at
        FROM contact 
        ORDER BY created_at DESC
    """)
    contacts = cursor.fetchall()
    conn.close()
    
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>All Contacts - Travela Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-dark bg-primary">
            <div class="container">
                <span class="navbar-brand mb-0 h1">
                    <i class="fa fa-envelope me-2"></i>All Contacts
                </span>
                <a href="/" class="btn btn-outline-light">
                    <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Contact Form Submissions</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Type</th>
                                    <th>Subject</th>
                                    <th>Message</th>
                                </tr>
                            </thead>
                            <tbody>
    """
    
    for contact in contacts:
        html += f"""
                                <tr>
                                    <td>{contact[7]}</td>
                                    <td>{contact[0]} {contact[1]}</td>
                                    <td>{contact[2]}</td>
                                    <td>{contact[3] or 'N/A'}</td>
                                    <td>{contact[4]}</td>
                                    <td>{contact[5]}</td>
                                    <td>{contact[6][:100]}{'...' if len(contact[6]) > 100 else ''}</td>
                                </tr>
        """
    
    html += """
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html

@app.route('/bookings')
def view_bookings():
    """View all bookings"""
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT booking_id, first_name, last_name, email, phone, destination, 
               package_type, departure_date, adults, children, created_at
        FROM booking 
        ORDER BY created_at DESC
    """)
    bookings = cursor.fetchall()
    conn.close()
    
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>All Bookings - Travela Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-dark bg-primary">
            <div class="container">
                <span class="navbar-brand mb-0 h1">
                    <i class="fa fa-calendar-check me-2"></i>All Bookings
                </span>
                <a href="/" class="btn btn-outline-light">
                    <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Booking Requests</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Destination</th>
                                    <th>Package</th>
                                    <th>Departure</th>
                                    <th>Travelers</th>
                                </tr>
                            </thead>
                            <tbody>
    """
    
    for booking in bookings:
        html += f"""
                                <tr>
                                    <td><strong>{booking[0]}</strong></td>
                                    <td>{booking[10]}</td>
                                    <td>{booking[1]} {booking[2]}</td>
                                    <td>{booking[3]}</td>
                                    <td>{booking[4]}</td>
                                    <td>{booking[5]}</td>
                                    <td>{booking[6]}</td>
                                    <td>{booking[7]}</td>
                                    <td>{booking[8]}A{f', {booking[9]}C' if booking[9] > 0 else ''}</td>
                                </tr>
        """
    
    html += """
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html

if __name__ == '__main__':
    print("🌟 Starting Simple Admin Panel...")
    print("📊 Access at: http://localhost:3000")
    print("⏹️  Press Ctrl+C to stop")
    app.run(debug=True, host='0.0.0.0', port=3000)
