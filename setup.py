#!/usr/bin/env python3
"""
Travela Tourism Website Backend Setup Script
This script helps you set up the Python Flask backend for your tourism website.
"""

import os
import sys
import subprocess
import sqlite3
from datetime import datetime

def print_header():
    print("=" * 60)
    print("🌟 TRAVELA TOURISM WEBSITE BACKEND SETUP 🌟")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("📋 Checking Python version...")
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required!")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def install_requirements():
    """Install required Python packages"""
    print("\n📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages!")
        print("   Please run: pip install -r requirements.txt")
        return False

def setup_environment():
    """Set up environment variables"""
    print("\n🔧 Setting up environment...")
    
    if not os.path.exists('.env'):
        print("📝 Creating .env file from template...")
        
        # Copy example file
        with open('.env.example', 'r') as example:
            content = example.read()
        
        with open('.env', 'w') as env_file:
            env_file.write(content)
        
        print("✅ .env file created!")
        print("⚠️  IMPORTANT: Please edit .env file with your actual email credentials!")
        print("   - Update MAIL_USERNAME with your Gmail address")
        print("   - Update MAIL_PASSWORD with your Gmail App Password")
        print("   - Update BUSINESS_EMAIL with your business email")
    else:
        print("✅ .env file already exists")

def setup_database():
    """Initialize the database"""
    print("\n💾 Setting up database...")
    
    try:
        # Import app to create database
        from app import app, db
        
        with app.app_context():
            db.create_all()
            print("✅ Database created successfully!")
            
            # Add some sample data for testing
            print("📝 Adding sample data...")
            
            # You can add sample data here if needed
            # For now, we'll just create the tables
            
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def update_html_files():
    """Update HTML files to include backend JavaScript"""
    print("\n🔗 Updating HTML files for backend integration...")
    
    html_files = ['booking.html', 'contact.html']
    
    for filename in html_files:
        if os.path.exists(filename):
            print(f"📝 Updating {filename}...")
            
            with open(filename, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Add backend.js script if not already present
            if 'backend.js' not in content:
                # Find the closing body tag and add script before it
                script_tag = '        <script src="js/backend.js"></script>\n    </body>'
                content = content.replace('    </body>', script_tag)
                
                with open(filename, 'w', encoding='utf-8') as file:
                    file.write(content)
                
                print(f"✅ {filename} updated with backend integration")
            else:
                print(f"✅ {filename} already has backend integration")

def create_run_script():
    """Create a simple run script"""
    print("\n🚀 Creating run script...")
    
    run_script_content = """#!/usr/bin/env python3
\"\"\"
Simple script to run the Travela backend server
\"\"\"

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == '__main__':
    try:
        from app import app
        print("🌟 Starting Travela Backend Server...")
        print("📱 Frontend: http://localhost:8000")
        print("🔧 Backend API: http://localhost:5000")
        print("👨‍💼 Admin Panel: http://localhost:5000/admin")
        print("⏹️  Press Ctrl+C to stop")
        print("-" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please run setup.py first!")
    except KeyboardInterrupt:
        print("\\n👋 Server stopped!")
"""
    
    with open('run_backend.py', 'w') as f:
        f.write(run_script_content)
    
    # Make it executable on Unix systems
    if os.name != 'nt':
        os.chmod('run_backend.py', 0o755)
    
    print("✅ Run script created: run_backend.py")

def print_instructions():
    """Print final setup instructions"""
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETE!")
    print("=" * 60)
    print()
    print("📋 NEXT STEPS:")
    print()
    print("1. 📧 Configure Email (IMPORTANT!):")
    print("   - Edit .env file")
    print("   - Add your Gmail credentials")
    print("   - Enable 2FA and create App Password")
    print()
    print("2. 🚀 Start the Backend Server:")
    print("   python run_backend.py")
    print("   OR")
    print("   python app.py")
    print()
    print("3. 🌐 Access Your Website:")
    print("   - Frontend: http://localhost:8000")
    print("   - Backend API: http://localhost:5000")
    print("   - Admin Panel: http://localhost:5000/admin")
    print()
    print("4. 🧪 Test the Forms:")
    print("   - Try submitting contact form")
    print("   - Try submitting booking form")
    print("   - Check admin panel for submissions")
    print()
    print("📧 EMAIL SETUP GUIDE:")
    print("   1. Go to Google Account settings")
    print("   2. Enable 2-Factor Authentication")
    print("   3. Generate App Password for 'Mail'")
    print("   4. Use App Password in .env file")
    print()
    print("🎯 Your tourism website backend is ready!")
    print("=" * 60)

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Setup environment
    setup_environment()
    
    # Setup database
    if not setup_database():
        return False
    
    # Update HTML files
    update_html_files()
    
    # Create run script
    create_run_script()
    
    # Print final instructions
    print_instructions()
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed! Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Setup completed successfully!")
