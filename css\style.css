
/*** Spinner Start ***/
/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}
/*** Spinner End ***/

.back-to-top {
    position: fixed;
    right: 30px;
    bottom: 30px;
    display: flex;
    width: 45px;
    height: 45px;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
    z-index: 99;
}

/*** Button Start ***/
.btn {
    font-weight: 600;
    transition: .5s;
}

.btn-square {
    width: 32px;
    height: 32px;
}

.btn-sm-square {
    width: 34px;
    height: 34px;
}

.btn-md-square {
    width: 44px;
    height: 44px;
}

.btn-lg-square {
    width: 56px;
    height: 56px;
}

.btn-square,
.btn-sm-square,
.btn-md-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    border-radius: 50%;
}

.btn.btn-primary {
    box-shadow: inset 0 0 0 0 var(--bs-primary);
}

.btn.btn-primary:hover {
    box-shadow: inset 300px 0 0 0 var(--bs-light) !important;
    color: var(--bs-primary) !important;
}

.btn.btn-light {
    box-shadow: inset 0 0 0 0 var(--bs-primary);
}

.btn.btn-light:hover {
    box-shadow: inset 300px 0 0 0 var(--bs-primary);
    color: var(--bs-light) !important;
}

.btn-hover {
    transition: 0.5s;
}

.btn-hover:hover {
    color: var(--bs-secondary) !important;
}

/*** Section Title Start ***/
.section-title {
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    color: var(--bs-primary);
}

.section-title::before {
    content: "";
    width: 50px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    margin-right: -50px;
    border: 1px solid var(--bs-primary) !important;
}

.section-title::after {
    content: "";
    width: 50px;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin-left: -50px;
    border: 1px solid var(--bs-primary) !important;
}


/*** Topbar Start ***/
.fixed-top .container {
    transition: 0.5s;
}

.topbar {
    padding: 2px 10px 2px 20px;
    background: var(--bs-primary) !important;
}

.topbar a,
.topbar a i {
    transition: 0.5s;
}

.topbar a:hover,
.topbar a i:hover {
    color: var(--bs-secondary) !important;
}


@media (max-width: 768px) {
    .topbar {
        display: none;    
    }
}
/*** Topbar End ***/


/*** Navbar ***/
.navbar-light .navbar-nav .nav-link {
    font-family: 'Roboto', sans-serif;
    position: relative;
    padding: 35px 15px;
    color: var(--bs-white) !important;
    font-size: 17px;
    font-weight: 400;
    outline: none;
    transition: .5s;
}

.sticky-top.navbar-light .navbar-nav .nav-link {
    padding: 20px 15px;
    color: var(--bs-dark) !important;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link.active {
    color: var(--bs-white) !important;
}

.navbar-light .navbar-brand h1 {
    color: var(--bs-white);
}

.sticky-top.navbar-light .navbar-brand h1 {
    color: var(--bs-primary);
}

.navbar-light .navbar-brand img {
    max-height: 60px;
    transition: .5s;
}

.sticky-top.navbar-light .navbar-brand img {
    max-height: 45px;
}

.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
}

@media (min-width: 1200px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
        border: 0;
        border-radius: 10px;
        transition: .5s;
        opacity: 0;
    }
}

.dropdown .dropdown-menu a:hover {
    background: var(--bs-primary);
    color: var(--bs-white);
}

.navbar .nav-item:hover .dropdown-menu {
    transform: rotateX(0deg);
    visibility: visible;
    background: var(--bs-light);
    transition: .5s;
    opacity: 1;
}

@media (max-width: 991.98px) {
    .sticky-top.navbar-light {
        position: relative;
        background: var(--bs-white);
    }

    .navbar-light .navbar-brand h1 {
        color: var(--bs-primary);
    }

    .navbar.navbar-expand-lg .navbar-toggler {
        padding: 10px 20px;
        border: 1px solid var(--bs-primary);
        color: var(--bs-primary);
    }

    .navbar-light .navbar-collapse {
        margin-top: 15px;
        border-top: 1px solid #DDDDDD;
    }

    .navbar-light .navbar-nav .nav-link,
    .sticky-top.navbar-light .navbar-nav .nav-link {
        padding: 10px 0;
        color: var(--bs-dark) !important;
    }

    .navbar-light .navbar-nav .nav-link:hover,
    .navbar-light .navbar-nav .nav-link.active  {
        color: var(--bs-primary) !important;
    }

    .navbar-light .navbar-brand img {
        max-height: 45px;
    }
}

@media (min-width: 992px) {
    .navbar-light {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        border-bottom: 1px solid rgba(255, 255, 255, .1);
        z-index: 999;
    }
    
    .sticky-top.navbar-light {
        position: fixed;
        background: var(--bs-light);
    }

    .navbar-light .navbar-nav .nav-link::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 0;
        bottom: -1px;
        left: 0;
        background: var(--bs-primary);
        transition: .5s;
        z-index: -1;
    }

    .navbar-light .navbar-nav .nav-link:hover::before,
    .navbar-light .navbar-nav .nav-link.active::before {
        height: calc(100% + 1px);
        left: 1px;
    }

    .navbar-light .navbar-nav .nav-link.nav-contact::before {
        display: none;
    }
}

/*** Carousel Hero Header Start ***/
.carousel-header .carousel-control-prev,
.carousel-header .carousel-control-next {
    background: transparent;
}

.carousel-header .carousel-control-prev .carousel-control-prev-icon {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    padding: 25px 30px;
    border-top-left-radius: 0;
    border-top-right-radius: 50px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 50px;
    background-size: 60% 60%;
}

.carousel-header .carousel-control-next .carousel-control-next-icon {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    padding: 25px 30px;
    border-top-left-radius: 50px;
    border-top-right-radius: 0;
    border-bottom-left-radius: 50px;
    border-bottom-right-radius: 0;
    background-size: 60% 60%;

}

.carousel-header .carousel .carousel-indicators li,
.carousel-header .carousel .carousel-indicators li,
.carousel-header .carousel .carousel-indicators li {
    opacity: 0;
}

.carousel-header .carousel-inner .carousel-item {
    position: relative;
    min-height: 100vh 
}

.carousel-header .carousel-inner .carousel-item img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-header .carousel-inner .carousel-item .carousel-caption  {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding-top: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, 0.5));
    background-size: cover;
}

@media (max-width: 768px) {
    .carousel-header .carousel-inner .carousel-item img,
    .carousel-header .carousel-inner .carousel-item .carousel-caption {
        height: 700px;
        margin-top: -100px;
    }

    .carousel-header {
        height: 700px !important;
    }

    .carousel-header .carousel-control-prev .carousel-control-prev-icon,
    .carousel-header .carousel-control-next .carousel-control-next-icon {
        opacity: 0;
    }
    
    .search-bar {
        margin-top: -100px;
        transition: 0.5s;
    }
}
/*** Carousel Hero Header End ***/


/*** Single Page Hero Header Start ***/
.bg-breadcrumb {
    background: linear-gradient(rgba(19, 53, 123, 0.5), rgba(19, 53, 123, 0.5)), url(../img/breadcrumb-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    padding: 150px 0 50px 0;
}

.bg-breadcrumb .breadcrumb-item a {
    color: var(--bs-secondary) !important;
}
/*** Single Page Hero Header End ***/


/*** About Start ***/
.about .container .section-about-title {
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    color: var(--bs-primary);
}

.about .container .section-about-title::before {
    content: "";
    width: 50px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    margin-right: -50px;
    border: 1px solid var(--bs-primary) !important;
}
/*** About End ***/


/*** Services Start ***/
.service .service-content-inner {
    transition: 0.5s;
}
.service .service-content-inner:hover {
    position: relative;
    background: var(--bs-primary) !important;
}

.service .service-content-inner .service-content h5,
.service .service-content-inner .service-content p,
.service .service-content-inner .service-icon i {
    transition: 0.5s;
}

.service .service-content-inner:hover .service-content h5,
.service .service-content-inner:hover .service-content p,
.service .service-content-inner:hover .service-icon i {
    color: var(--bs-white) !important;
}
/*** Service End ***/


/*** Destination Start ***/
.destination .tab-class .tab-content .tab-pane .destination-img {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    z-index: 1;
}

.destination .tab-class .tab-content .tab-pane .destination-img .destination-overlay {
    position: absolute;
    bottom: -100%;
    left: 0;
    z-index: 3;
    transition: 0.5s;
}

.destination .tab-class .tab-content .tab-pane .destination-img .search-icon {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    right: 0;
    display: flex;
    justify-content: end;
    padding: 20px 20px 0 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    transition: 0.5s;

}

.destination .tab-class .tab-content .tab-pane .destination-img .search-icon a i {
    opacity: 0;
    transition: 0.5s;
}

.destination .tab-class .nav-item {
    padding: 0 0 20px 0;
}
.destination .tab-class .nav-item a.active {
    background: var(--bs-primary) !important;
}

.destination .tab-class .nav-item a.active span {
    color: var(--bs-white) !important;
}

.destination .tab-class .tab-content .destination-img:hover .search-icon {
    background: rgba(19, 53, 123, 0.4);
}

.destination .tab-class .tab-content .destination-img:hover .destination-overlay {
    bottom: 0;
}

.destination .tab-class .tab-content .destination-img:hover .search-icon a i {
    opacity: 1;
}

.destination .tab-class .tab-content .destination-img img {
    transition: 0.5s;
}

.destination .tab-class .tab-content .destination-img:hover img {
    transform: scale(1.2);
}
/*** Destination End ***/


/*** Packages Start ***/
.packages .packages-item .packages-img {
    position: relative;
    overflow: hidden;
    transition: 0.5s;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    z-index: 1;
}

.packages .packages-item .packages-img .packages-info {
    background: rgba(0, 0, 0, .3);
}

.packages .packages-item .packages-img .packages-info small,
.packages .packages-item .packages-img .packages-info small i {
    color: var(--bs-white);
    transition: 0.5s;
}

.packages .packages-item .packages-img::after {
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 0px solid;
    border-radius: 10px !important;
    visibility: hidden;
    transition: 0.7s;
    z-index: 3;
}

.packages .packages-item .packages-img:hover.packages-img::after {
    width: 100%;
    height: 100%;
    border: 300px solid;
    border-color: rgba(19, 53, 123, 0.6) rgba(19, 53, 123, 0.6) rgba(19, 53, 123, 0.6) rgba(19, 53, 123, 0.6);
    visibility: visible;
}

.packages .packages-item .packages-img small,
.packages .packages-item .packages-img small i {
    transition: 0.5s;
}

.packages .packages-item .packages-img:hover small,
.packages .packages-item .packages-img:hover small i {
    color: var(--bs-white) !important;

}

.packages .packages-item .packages-img img {
    transition: 0.5s;
}

.packages .packages-item .packages-img:hover img {
    transform: scale(1.3);
}

.packages .packages-item .packages-img .packages-price {
    position: absolute;
    width: 100px; 
    top: 0; 
    left: 50%; 
    transform: translateX(-50%);
    display: inline-block;
    background: var(--bs-primary);
    color: var(--bs-white);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px; 
    z-index: 5;
}

.packages .packages-carousel {
    position: relative;
}

.packages .packages-carousel .owl-nav .owl-prev {
    position: absolute;
    top: -50px;
    left: 0;
    padding: 5px 30px;
    border: 1px solid var(--bs-primary);
    border-radius: 30px;
    transition: 0.5s;
}

.packages .packages-carousel .owl-nav .owl-next {
    position: absolute;
    top: -50px;
    right: 0;
    padding: 5px 30px;
    border: 1px solid var(--bs-primary);
    border-radius: 30px;
    transition: 0.5s;
}

.packages .packages-carousel .owl-nav .owl-prev i,
.packages .packages-carousel .owl-nav .owl-next i {
    color: var(--bs-primary);
    font-size: 17px;
    transition: 0.5s;
}

.packages .packages-carousel .owl-nav .owl-prev:hover,
.packages .packages-carousel .owl-nav .owl-next:hover {
    background: var(--bs-primary);
}

.packages .packages-carousel .owl-nav .owl-prev:hover i,
.packages .packages-carousel .owl-nav .owl-next:hover i {
    color: var(--bs-white);
}
/*** Packages End ***/


/*** Explore Tour Start ***/
.ExploreTour .tab-class .nav-item {
    padding: 0 0 20px 0;
}
.ExploreTour .tab-class .nav-item a.active {
    background: var(--bs-primary) !important;
}

.ExploreTour .tab-class .nav-item a.active span {
    color: var(--bs-white) !important;
}


/* National Tour Start */
.ExploreTour #NationalTab-1 .national-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.ExploreTour #NationalTab-1 .national-item img {
    transition: 0.5s;
}

.ExploreTour #NationalTab-1 .national-item:hover img {
    transform: scale(1.2);
}

.ExploreTour #NationalTab-1 .national-item .national-content {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 10px;
    padding: 20px;
    background: rgba(0, 0, 0, .2);
    display: flex;
    align-items: end;
    justify-content: center;
    transition: 0.5s;
}

.ExploreTour #NationalTab-1 .national-item:hover .national-content {
    background: rgba(19, 53, 123, .6);
}

.ExploreTour #NationalTab-1 .national-item .national-plus-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: 0.5s;
    opacity: 0;
}

.ExploreTour #NationalTab-1 .national-item:hover .national-plus-icon {
    opacity: 1;
}

.ExploreTour #NationalTab-1 .national-item .tour-offer {
    position: absolute;
    top: -1px;
    left: -1px;
    padding: 20px;
    border-top-right-radius: 40px;
    border-top-left-radius: 20px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 80px;
    background: var(--bs-primary);
    color: var(--bs-white);
}

/* International Tour Start */
.ExploreTour #InternationalTab-2 .international-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.ExploreTour #InternationalTab-2 .international-item img {
    transition: 0.5s;
}

.ExploreTour #InternationalTab-2 .international-item:hover img {
    transform: scale(1.2);
}

.ExploreTour #InternationalTab-2 .international-item .tour-offer {
    position: absolute;
    top: -1px;
    left: -1px;
    padding: 20px;
    border-top-right-radius: 30px;
    border-top-left-radius: 20px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 80px;
    background: var(--bs-primary);
    color: var(--bs-white);
}

.ExploreTour #InternationalTab-2 .international-item .international-content {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 10px;
    padding: 20px;
    background: rgba(0, 0, 0, .3);
    display: flex;
    align-items: end;
    justify-content: center;
    transition: 0.5s;
}

.ExploreTour #InternationalTab-2 .international-item:hover .international-content {
    background: rgba(19, 53, 123, .6);
}

.ExploreTour #InternationalTab-2 .international-item .international-content .international-info a {
    font-size: 14px;
}

.ExploreTour #InternationalTab-2 .international-item .international-plus-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: 0.5s;
    opacity: 0;
}

.ExploreTour #InternationalTab-2 .international-item:hover .international-plus-icon {
    opacity: 1;
}

/* carousel Start */
.ExploreTour #InternationalTab-2 .InternationalTour-carousel .international-item {
    position: relative;
    overflow: hidden;
}

.ExploreTour #InternationalTab-2 .InternationalTour-carousel .owl-dots {
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ExploreTour #InternationalTab-2 .InternationalTour-carousel .owl-dot {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    width: 15px;
    height: 15px;
    background: var(--bs-light);
    border: 1px solid var(--bs-primary);
    border-radius: 10px;
    transition: 0.5s;
}

.ExploreTour #InternationalTab-2 .InternationalTour-carousel .owl-dot.active {
    width: 40px;
    background: var(--bs-primary);
}
/*** Explore Tour End ***/


/*** Gallery Start ***/
.gallery .gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.gallery .gallery-item img {
    min-height: 300px;
    object-fit: cover;
}

.gallery .gallery-item .gallery-content {
    position: absolute;
    width: 100%;
    height: 100%;
    bottom: 0;
    left: 0;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: end;
    transition: 0.5s;
}

.gallery .gallery-item .gallery-content .gallery-info {
    position: relative;
    margin-bottom: -100%;
    opacity: 0;
    transition: 0.5s;
}

.gallery .gallery-item .gallery-plus-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: 0.5s;
    opacity: 0;
}

.gallery .gallery-item:hover .gallery-content .gallery-info,
.gallery .gallery-item:hover .gallery-plus-icon {
   opacity: 1;
   margin: 0;
}

.gallery .gallery-item img {
    transition: 0.5s;
}

.gallery .gallery-item:hover img {
    transform: scale(1.2);
}

.gallery .gallery-item:hover .gallery-content {
    background: rgba(19, 53, 123, 0.8);
}

.gallery .tab-class .nav-item {
    padding: 0 0 20px 0;
}
.gallery .tab-class .nav-item a.active {
    background: var(--bs-primary) !important;
}

.gallery .tab-class .nav-item a.active span {
    color: var(--bs-white) !important;
}

/*** Gallery End ***/

/*** Tour Booking Start ***/
.booking {
    background: linear-gradient(rgba(19, 53, 123, .8), rgba(19, 53, 123, .8)), url(../img/tour-booking-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    
}


.booking .container .section-booking-title {
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    color: var(--bs-white);
}

.booking .container .section-booking-title::before {
    content: "";
    width: 50px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    margin-right: -50px;
    border: 1px solid var(--bs-white) !important;
}

.booking .container form .btn.btn-primary {
    box-shadow: inset 0 0 0 0 var(--bs-primary);
}

.booking .container form .btn.btn-primary:hover {
    box-shadow: inset 800px 0 0 0 var(--bs-light) !important;
    color: var(--bs-primary) !important;
}



/*** Tour Booking end ***/

/*** Travel Guide Start ***/
.guide .guide-item .guide-img {
    position: relative;
}

.guide .guide-item .guide-img .guide-icon {
    position: absolute;
    bottom: 0; 
    left: 50%; 
    transform: translate(-50%, -50%);
    margin-bottom: -50px;
    display: flex;
    justify-content: center;
    border: 1px solid var(--bs-primary);
    background: var(--bs-light);
    z-index: 9;
}

.guide .guide-item .guide-img .guide-img-efects {
    position: relative;
    overflow: hidden;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
}

.guide .guide-item .guide-img .guide-img-efects::after {
    content: "";
    width: 100%;
    height: 0;
    position: absolute;
    top: 0;
    left: 0;
    transition: 0.5s;
}

.guide .guide-item:hover .guide-img-efects::after {
    height: 100%;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    background: rgba(19, 53, 123, .5);
}

.guide .guide-item .guide-img-efects img {
    transition: 0.5s;
}
.guide .guide-item:hover .guide-img-efects img {
    transform: scale(1.1);
}

.guide .guide-item .guide-title {
    position: relative;
    background: var(--bs-light);
    transition: 0.5s;
}

.guide .guide-item .guide-title::after {
    content: "";
    width: 100%;
    height: 0;
    position: absolute;
    bottom: 0;
    left: 0;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    transition: 0.5s;
}

.guide .guide-item:hover .guide-title::after {
    height: 100%;
    background: var(--bs-primary) !important;
    color: var(--bs-white);
}

.guide .guide-item .guide-title .guide-title-inner,
.guide .guide-item:hover .guide-title .guide-title-inner h4 {
    transition: 0.5s;
}

.guide .guide-item:hover .guide-title .guide-title-inner {
    position: relative;
    color: var(--bs-white) !important;
    z-index: 2;
}

.guide .guide-item:hover .guide-title .guide-title-inner h4 {
    color: var(--bs-white);
}
/*** Travel Guide End ***/


/*** Blog Start ***/
.blog .blog-item .blog-img {
    position: relative;
}

.blog .blog-item .blog-img .blog-info {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    display: flex;
    background: rgba(255, 255, 255, .2);
    color: var(--bs-white);
}

.blog .blog-item .blog-img .blog-img-inner {
    position: relative;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
}

.blog .blog-item .blog-img .blog-img-inner .blog-icon {
    position: absolute;
    width: 100%;
    height: 0;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
}

.blog .blog-item .blog-img .blog-img-inner .blog-icon a {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: 0.5s;
}

.blog .blog-item .blog-img:hover .blog-img-inner .blog-icon a {
    opacity: 1;
}

.blog .blog-item .blog-img:hover .blog-img-inner .blog-icon {
    height: 100%;
    background: rgba(19, 53, 123, .6);
    opacity: 1;
}

.blog .blog-item .blog-img .blog-img-inner {
    overflow: hidden;
}

.blog .blog-item .blog-img .blog-img-inner img {
    transition: 0.5s;
}

.blog .blog-item .blog-img:hover .blog-img-inner img {
    transform: scale(1.2);
}
/*** Blog End ***/

/*** Testimonial Start ***/
.testimonial .testimonial-carousel {
    position: relative;
}

.testimonial .testimonial-carousel .testimonial-item .testimonial-img {
    position: relative;
    width: 100px; 
    height: 100px; 
    top: 0; left: 50%; 
    transform: translate(-50%, -50%);
    border: 3px solid var(--bs-primary); 
    border-style: dotted;
    border-radius: 50%;
}

.testimonial .testimonial-carousel .owl-dots {
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial .testimonial-carousel .owl-dot {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    width: 15px;
    height: 15px;
    background: var(--bs-light);
    border: 1px solid var(--bs-primary);
    border-radius: 10px;
    transition: 0.5s;
}

.testimonial .testimonial-carousel .owl-dot.active {
    width: 40px;
    background: var(--bs-primary);
}

.testimonial .testimonial-carousel .owl-nav .owl-prev {
    position: absolute;
    top: -55px;
    left: 0;
    padding: 5px 30px;
    border: 1px solid var(--bs-primary);
    border-radius: 30px;
    transition: 0.5s;
}

.testimonial .testimonial-carousel .owl-nav .owl-next {
    position: absolute;
    top: -55px;
    right: 0;
    padding: 5px 30px;
    border: 1px solid var(--bs-primary);
    border-radius: 30px;
    transition: 0.5s;
}

.testimonial .testimonial-carousel .owl-nav .owl-prev i,
.testimonial .testimonial-carousel .owl-nav .owl-next i {
    color: var(--bs-primary);
    font-size: 17px;
    transition: 0.5s;
}

.testimonial .testimonial-carousel .owl-nav .owl-prev:hover,
.testimonial .testimonial-carousel .owl-nav .owl-next:hover {
    background: var(--bs-primary);
}

.testimonial .testimonial-carousel .owl-nav .owl-prev:hover i,
.testimonial .testimonial-carousel .owl-nav .owl-next:hover i {
    color: var(--bs-white);
}

.testimonial .testimonial-carousel .owl-item.center .testimonial-item .testimonial-comment {
    background: var(--bs-primary) !important;
    color: var(--bs-white);
    transition: 0.5s;
}

.testimonial .testimonial-carousel .owl-item.center .testimonial-item .testimonial-img {
    border: 3px solid var(--bs-white); 
    border-style: dotted;
    transition: 0.5s;
}
/*** Testimonial End ***/

/*** Contact Start ***/
.contact .container form .btn.btn-primary {
    box-shadow: inset 0 0 0 0 var(--bs-primary);
}

.contact .container form .btn.btn-primary:hover {
    box-shadow: inset 1000px 0 0 0 var(--bs-light) !important;
    color: var(--bs-primary) !important;
}
/*** Contact End ***/

/*** Subscribe Start ***/
.subscribe {
    background: linear-gradient(rgba(19, 53, 123, .6), rgba(19, 53, 123, .6)), url(../img/subscribe-img.jpg);
    background-attachment: fixed;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}

.subscribe .subscribe-title {
    position: relative;
    display: inline-block;
    text-transform: uppercase;
    color: var(--bs-white);
}

.subscribe .subscribe-title::before {
    content: "";
    width: 50px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    margin-right: -50px;
    border: 1px solid var(--bs-white) !important;
}

.subscribe .subscribe-title::after {
    content: "";
    width: 50px;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin-left: -50px;
    border: 1px solid var(--bs-white) !important;
}
/*** Subscribe End ***/

/*** Footer Start ***/
.footer {
    background: var(--bs-dark);
}
.footer .footer-item a {
    line-height: 30px;
    color: var(--bs-white);
    transition: 0.5s;
}

.footer .footer-item a:hover {
    letter-spacing: 2px;
    color: var(--bs-primary);
}

/*** Footer End ***/

/*** copyright Start ***/
.copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    background: var(--bs-dark) !important;
}
/*** copyright end ***/

/*** Modern Tour Page Styles Start ***/
/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.stats-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

.stats-icon {
    transition: all 0.3s ease;
}

.stats-card:hover .stats-icon i {
    transform: scale(1.2);
}

/* Modern Filter Buttons */
.filter-btn-modern {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 50px;
    padding: 12px 24px;
    margin: 5px;
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.filter-btn-modern:hover {
    border-color: var(--bs-primary);
    color: var(--bs-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.2);
}

.filter-btn-modern.active {
    background: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

.filter-btn-modern .badge {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    border-radius: 20px;
    padding: 4px 8px;
    font-size: 11px;
    margin-left: 8px;
}

.filter-btn-modern.active .badge {
    background: rgba(255, 255, 255, 0.3);
}

/* Modern Tabs */
.modern-tabs {
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.tab-navigation {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 8px;
    position: relative;
}

.tab-btn {
    background: transparent;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
    position: relative;
    margin: 0 5px;
    display: flex;
    align-items: center;
}

.tab-btn.active {
    background: white;
    color: var(--bs-primary);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: var(--bs-primary);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.tab-btn.active .tab-indicator {
    width: 30px;
}

/* Modern Tour Cards */
.modern-tour-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-tour-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.card-image-container {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.modern-tour-card:hover .card-image {
    transform: scale(1.1);
}

.card-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-badge.weekend {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.card-badge.luxury {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.card-badge.adventure {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.card-badge.family {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

.card-discount {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #dc3545;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.modern-tour-card:hover .card-overlay {
    opacity: 1;
}

.quick-view-btn {
    background: white;
    color: var(--bs-primary);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quick-view-btn:hover {
    background: var(--bs-primary);
    color: white;
    transform: scale(1.05);
}
/* Card Content */
.card-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.card-header {
    margin-bottom: 15px;
}

.card-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.card-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stars {
    color: #ffc107;
}

.rating-text {
    font-size: 14px;
    color: #6c757d;
}

.card-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
}

.detail-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.card-description {
    margin-bottom: 20px;
    flex-grow: 1;
}

.card-description p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}

.card-footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.price-section {
    margin-bottom: 15px;
}

.price-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--bs-primary);
}

.original-price {
    font-size: 1.2rem;
    color: #6c757d;
    text-decoration: line-through;
    margin-right: 10px;
}

.price-unit {
    font-size: 14px;
    color: #6c757d;
    margin-left: 5px;
}

.card-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.btn-wishlist {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #6c757d;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-wishlist:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: scale(1.1);
}

.btn-wishlist.active {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-book {
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    flex-grow: 1;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-book:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

/* Tab Content */
.tab-content-modern {
    display: none;
}

.tab-content-modern.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-btn-modern {
        padding: 10px 16px;
        font-size: 14px;
    }

    .tab-btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .card-image-container {
        height: 200px;
    }

    .card-content {
        padding: 20px;
    }

    .card-title {
        font-size: 1.2rem;
    }

    .price {
        font-size: 1.5rem;
    }

    .card-details {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .stats-card {
        margin-bottom: 20px;
    }

    .filter-btn-modern {
        width: 100%;
        justify-content: center;
        margin: 5px 0;
    }

    .tab-navigation {
        flex-direction: column;
    }

    .tab-btn {
        width: 100%;
        justify-content: center;
        margin: 5px 0;
    }
}
/*** Modern Tour Page Styles End ***/

/*** Enhanced Destination Section Start ***/
/* Hero Background */
.destination-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.destination-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 100px 100px;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Header Styling */
.destination-header {
    position: relative;
    z-index: 2;
}

.header-icon-wrapper {
    position: relative;
}

.floating-icon {
    display: inline-block;
    animation: floatIcon 3s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.destination-subtitle {
    font-size: 1.1rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.destination-main-title {
    font-size: 3.5rem;
    font-weight: 800;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 1rem;
}

.destination-question {
    font-size: 2rem;
    font-weight: 300;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.destination-description {
    font-size: 1.1rem;
    line-height: 1.8;
    font-weight: 300;
}

/* Stats Section */
.destination-stats {
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Enhanced Slider */
.enhanced-destination-slider {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
}

.slider-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.slider-track {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
}

/* Modern Destination Cards */
.enhanced-destination-slide {
    display: none;
    animation: slideIn 0.6s ease-out;
}

.enhanced-destination-slide.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.modern-destination-card {
    position: relative;
    height: 500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
}

.modern-destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.card-image-bg {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.4s ease;
}

.modern-destination-card:hover .card-image-bg {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 0.8) 0%,
        rgba(118, 75, 162, 0.8) 100%
    );
}

.card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 40px;
    color: white;
}

.destination-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.modern-destination-card:hover .destination-icon {
    transform: scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
}

.destination-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.destination-tagline {
    font-size: 1.1rem;
    margin-bottom: 25px;
    opacity: 0.9;
    font-style: italic;
}

.destination-features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 25px;
}

.feature-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.feature-tag:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.destination-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.btn-explore {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    color: #667eea;
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-explore:hover {
    color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-favorite {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-favorite:hover {
    background: #dc3545;
    border-color: #dc3545;
    transform: scale(1.1);
}

.btn-favorite.active {
    background: #dc3545;
    border-color: #dc3545;
}
/* Responsive Design */
@media (max-width: 768px) {
    .destination-hero {
        min-height: 80vh;
        padding: 40px 0;
    }

    .destination-main-title {
        font-size: 2.5rem;
    }

    .destination-question {
        font-size: 1.5rem;
    }

    .destination-description {
        font-size: 1rem;
    }

    .destination-stats {
        margin-top: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .enhanced-destination-slider {
        margin: 0 15px;
    }

    .slider-container {
        padding: 20px;
    }

    .modern-destination-card {
        height: 400px;
    }

    .card-content {
        padding: 30px 20px;
    }

    .destination-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;
    }

    .destination-name {
        font-size: 1.8rem;
    }

    .destination-tagline {
        font-size: 1rem;
    }

    .destination-features {
        margin-bottom: 20px;
    }

    .feature-tag {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .destination-stats {
        gap: 20px;
        margin-bottom: 25px;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .btn-explore {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .btn-favorite {
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .destination-main-title {
        font-size: 2rem;
    }

    .destination-question {
        font-size: 1.3rem;
    }

    .destination-stats .col-6 {
        margin-bottom: 10px;
    }

    .stat-item {
        padding: 10px;
    }

    .modern-destination-card {
        height: 350px;
    }

    .card-content {
        padding: 25px 15px;
    }

    .destination-icon {
        width: 50px;
        height: 50px;
    }

    .destination-name {
        font-size: 1.5rem;
    }

    .destination-features {
        flex-direction: column;
        align-items: center;
    }

    .destination-stats {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .card-actions {
        flex-direction: column;
        gap: 10px;
    }

    .btn-explore {
        width: 100%;
        justify-content: center;
    }
}

/* Animation Enhancements */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.destination-icon:hover {
    animation: pulse 1s infinite;
}

/* Navigation Arrows for Enhanced Slider */
.destination-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.3);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.destination-nav:hover {
    background: #667eea;
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
}

.destination-prev {
    left: -30px;
}

.destination-next {
    right: -30px;
}

/* Dots Indicator for Enhanced Slider */
.destination-dots {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 30px;
}

.destination-dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.destination-dot.active {
    background: #667eea;
    transform: scale(1.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.destination-dot:hover {
    background: #667eea;
    transform: scale(1.2);
}
/*** Enhanced Destination Section End ***/

/*** Simple Tour Page Styles Start ***/
/* Simple tour card hover effects */
.tour-card {
    transition: all 0.3s ease;
}

.tour-card:hover {
    transform: translateY(-5px);
}

.tour-card .card {
    border: none;
    transition: all 0.3s ease;
}

.tour-card .card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Filter button styles */
.btn-group .btn {
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
}

.btn-group .btn.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

/* Card image hover effect */
.tour-card .card-img-top {
    transition: all 0.3s ease;
}

.tour-card:hover .card-img-top {
    transform: scale(1.05);
}

/* Price styling */
.tour-card .text-primary {
    font-weight: 700;
}

/* Button hover effects */
.tour-card .btn-primary {
    transition: all 0.3s ease;
}

.tour-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}
/*** Simple Tour Page Styles End ***/

/*** Simple Destination Cards Start ***/
/* Simple destination card hover effects */
.card {
    border: none;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Card image hover effect */
.card-img-top {
    transition: all 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Button hover effects */
.card .btn-primary {
    transition: all 0.3s ease;
}

.card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

/* Star rating styling */
.text-warning {
    color: #ffc107 !important;
}
/*** Simple Destination Cards End ***/

/*** Simple About Us Section Start ***/
/* About image container */
.about-image-container {
    position: relative;
}

.about-image-container img {
    transition: all 0.3s ease;
}

.about-image-container:hover img {
    transform: scale(1.02);
}

/* Experience badge */
.experience-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--bs-primary);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.experience-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.experience-text {
    font-size: 0.9rem;
    margin-top: 5px;
}

/* About content */
.about-content h2 {
    color: #2c3e50;
    font-weight: 600;
    line-height: 1.3;
}

.about-content p {
    color: #6c757d;
    line-height: 1.7;
}

/* Feature items */
.feature-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-item:hover {
    background: white;
    border-color: var(--bs-primary);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 40px;
    height: 40px;
    background: rgba(13, 110, 253, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.feature-item h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 2px;
}

/* Stats */
.stat-item {
    padding: 15px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-item h3 {
    font-weight: 700;
    margin-bottom: 5px;
}

/* Buttons */
.about-content .btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.about-content .btn:hover {
    transform: translateY(-2px);
}

.about-content .btn-primary:hover {
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.about-content .btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .experience-badge {
        bottom: 10px;
        right: 10px;
        padding: 15px;
    }

    .experience-number {
        font-size: 1.5rem;
    }

    .experience-text {
        font-size: 0.8rem;
    }

    .about-content h2 {
        font-size: 1.5rem;
    }

    .feature-item {
        margin-bottom: 10px;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 10px !important;
    }

    .d-flex.gap-3 .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .stat-item {
        margin-bottom: 15px;
    }

    .feature-item {
        padding: 12px;
    }

    .feature-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}
/*** Simple About Us Section End ***/

/*** Simple Services Section Start ***/
/* Service cards */
.service-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

/* Service icon wrapper */
.service-icon-wrapper {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon-wrapper {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, #0056b3, var(--bs-primary));
}

.service-icon {
    font-size: 2rem;
    color: white;
}

/* Service content */
.service-content h4 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

.service-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Service features list */
.service-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.service-features li {
    padding: 5px 0;
    color: #495057;
    font-size: 0.9rem;
}

.service-features i {
    font-size: 0.8rem;
}

/* Service buttons */
.service-card .btn {
    transition: all 0.3s ease;
    font-weight: 500;
    margin-top: 10px;
}

.service-card .btn:hover {
    transform: translateY(-2px);
}

.service-card .btn-outline-primary:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

/* Call to action section */
.text-center h3 {
    color: #2c3e50;
    font-weight: 600;
}

.d-flex.gap-3 .btn {
    transition: all 0.3s ease;
}

.d-flex.gap-3 .btn:hover {
    transform: translateY(-2px);
}

.d-flex.gap-3 .btn-primary:hover {
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.d-flex.gap-3 .btn-outline-primary:hover {
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .service-card {
        padding: 25px;
        margin-bottom: 20px;
    }

    .service-icon-wrapper {
        width: 70px;
        height: 70px;
        margin-bottom: 15px;
    }

    .service-icon {
        font-size: 1.8rem;
    }

    .service-content h4 {
        font-size: 1.3rem;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 10px !important;
    }

    .d-flex.gap-3 .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .service-card {
        padding: 20px;
    }

    .service-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .service-icon {
        font-size: 1.5rem;
    }

    .service-features {
        text-align: center;
    }
}
/*** Simple Services Section End ***/

/*** Enhanced Mobile Destination Section Start ***/
/* Mobile-optimized header with enhanced styling */
.mobile-title {
    font-size: 2rem;
    line-height: 1.2;
    font-weight: 800;
    background: linear-gradient(135deg, #2c3e50, var(--bs-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    text-align: center;
    position: relative;
}

.mobile-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border-radius: 2px;
}

.mobile-subtitle {
    font-size: 1.1rem;
    line-height: 1.4;
    color: #6c757d;
    font-weight: 400;
    text-align: center;
    margin-bottom: 30px;
}

/* Enhanced mobile filter tabs */
.mobile-filter-tabs {
    margin-bottom: 35px;
    position: relative;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    padding: 15px 0;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filter-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 10px 0;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    position: relative;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.filter-scroll-container::-webkit-scrollbar {
    display: none;
}

.filter-tabs-wrapper {
    display: flex;
    gap: 15px;
    padding: 0 25px;
    min-width: max-content;
    align-items: center;
}

.mobile-filter-tab {
    background: white;
    border: 3px solid transparent;
    border-radius: 25px;
    padding: 16px 28px;
    font-size: 1rem;
    font-weight: 700;
    color: #495057;
    white-space: nowrap;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    backdrop-filter: blur(10px);
}

.mobile-filter-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.mobile-filter-tab:active::before {
    left: 100%;
}

.mobile-filter-tab::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(13, 110, 253, 0.3), transparent);
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    border-radius: 50%;
    z-index: 0;
}

.mobile-filter-tab:active::after {
    width: 200px;
    height: 200px;
}

.mobile-filter-tab.active {
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
    position: relative;
    z-index: 2;
}

.mobile-filter-tab.active::before {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.mobile-filter-tab:hover:not(.active) {
    border-color: var(--bs-primary);
    color: var(--bs-primary);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.25);
    background: linear-gradient(135deg, white, #f8f9fa);
}

.mobile-filter-tab.active:hover {
    color: white;
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 10px 30px rgba(13, 110, 253, 0.5);
}

.mobile-filter-tab i {
    font-size: 1.1rem;
    margin-right: 10px;
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.mobile-filter-tab:hover i {
    transform: scale(1.1) rotate(5deg);
}

.mobile-filter-tab.active i {
    transform: scale(1.15);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.mobile-filter-tab span {
    position: relative;
    z-index: 2;
}

/* Scroll indicators */
.filter-scroll-container::before,
.filter-scroll-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 2;
}

.filter-scroll-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(248, 249, 250, 1), rgba(248, 249, 250, 0));
}

.filter-scroll-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(248, 249, 250, 1), rgba(248, 249, 250, 0));
}

/* Enhanced mobile destination cards */
.destination-mobile-card {
    margin-bottom: 25px;
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.6s ease forwards;
}

.destination-mobile-card:nth-child(1) { animation-delay: 0.1s; }
.destination-mobile-card:nth-child(2) { animation-delay: 0.2s; }
.destination-mobile-card:nth-child(3) { animation-delay: 0.3s; }
.destination-mobile-card:nth-child(4) { animation-delay: 0.4s; }
.destination-mobile-card:nth-child(5) { animation-delay: 0.5s; }
.destination-mobile-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 2px solid transparent;
}

.mobile-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.05), rgba(0, 86, 179, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    pointer-events: none;
}

.mobile-card:hover::before {
    opacity: 1;
}

.mobile-card:active {
    transform: scale(0.97);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.mobile-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(13, 110, 253, 0.3);
}

.mobile-card-image {
    position: relative;
    height: 220px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.mobile-card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
    z-index: 2;
    transition: opacity 0.3s ease;
}

.mobile-card:hover .mobile-card-image::before {
    opacity: 0.7;
}

.mobile-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.mobile-card:hover .mobile-card-image img {
    transform: scale(1.1) rotate(1deg);
}

.mobile-card:active .mobile-card-image img {
    transform: scale(1.05);
}

.mobile-card-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.95), rgba(0, 86, 179, 0.95));
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 700;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
    z-index: 3;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-card:hover .mobile-card-badge {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
}

.mobile-card-price {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 250, 0.98));
    color: var(--bs-primary);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: 800;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(13, 110, 253, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 3;
    transition: all 0.3s ease;
}

.mobile-card:hover .mobile-card-price {
    transform: scale(1.05) translateY(-2px);
    background: linear-gradient(135deg, white, #f8f9fa);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    color: #0056b3;
}

.mobile-card-content {
    padding: 25px;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, white, #fafbfc);
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    position: relative;
}

.mobile-card-header::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border-radius: 1px;
}

.mobile-card-header h4 {
    font-size: 1.4rem;
    font-weight: 800;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    flex: 1;
    line-height: 1.2;
}

.mobile-rating {
    text-align: right;
    flex-shrink: 0;
    margin-left: 15px;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 8px 12px;
    border-radius: 15px;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.rating-stars {
    color: #ffc107;
    font-size: 1rem;
    display: block;
    line-height: 1;
    text-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
    margin-bottom: 2px;
}

.rating-number {
    font-size: 0.85rem;
    color: #856404;
    font-weight: 700;
}

.mobile-card-description {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 20px;
    line-height: 1.5;
    font-weight: 500;
    font-style: italic;
}

.mobile-card-info {
    display: flex;
    gap: 25px;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 15px;
    border-radius: 15px;
    border: 1px solid rgba(13, 110, 253, 0.1);
}

.info-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #495057;
    font-weight: 600;
    flex: 1;
    justify-content: center;
}

.info-item i {
    color: var(--bs-primary);
    margin-right: 8px;
    font-size: 1rem;
    background: rgba(13, 110, 253, 0.1);
    padding: 6px;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-explore-btn {
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    color: white;
    padding: 16px 32px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    justify-content: center;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-explore-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.mobile-explore-btn:hover::before {
    left: 100%;
}

.mobile-explore-btn:hover,
.mobile-explore-btn:active {
    color: white;
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 10px 30px rgba(13, 110, 253, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.mobile-explore-btn:active {
    transform: translateY(-2px) scale(0.98);
}

.mobile-explore-btn i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.mobile-explore-btn:hover i {
    transform: translateX(5px);
}

/* Enhanced mobile view more button */
.mobile-view-more {
    font-weight: 700;
    padding: 18px 40px;
    border-radius: 30px;
    font-size: 1.1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    max-width: 350px;
    background: linear-gradient(135deg, white, #f8f9fa);
    border: 3px solid var(--bs-primary);
    color: var(--bs-primary);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.mobile-view-more::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    transition: left 0.4s ease;
    z-index: 1;
}

.mobile-view-more:hover::before {
    left: 0;
}

.mobile-view-more span,
.mobile-view-more i {
    position: relative;
    z-index: 2;
    transition: color 0.4s ease;
}

.mobile-view-more:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
    color: white;
}

.mobile-view-more:hover span,
.mobile-view-more:hover i {
    color: white;
}

.mobile-view-more i {
    transition: transform 0.3s ease;
}

.mobile-view-more:hover i {
    transform: translateX(8px);
}

/* Enhanced touch improvements */
@media (max-width: 768px) {
    .mobile-card {
        margin: 0 10px 30px 10px;
        border-radius: 25px;
    }

    .mobile-filter-tab {
        padding: 18px 28px;
        font-size: 1.05rem;
        min-width: 145px;
        border-radius: 30px;
    }

    .mobile-card-content {
        padding: 30px;
    }

    .mobile-explore-btn {
        padding: 18px 32px;
        font-size: 1.1rem;
        border-radius: 35px;
    }

    .filter-tabs-wrapper {
        gap: 18px;
        padding: 0 30px;
    }

    .mobile-card-image {
        height: 240px;
    }

    .mobile-title {
        font-size: 2.2rem;
    }

    .mobile-subtitle {
        font-size: 1.15rem;
    }
}

/* Enhanced extra small screens */
@media (max-width: 576px) {
    .mobile-title {
        font-size: 1.8rem;
        margin-bottom: 20px;
    }

    .mobile-title::after {
        width: 50px;
        height: 2px;
    }

    .mobile-subtitle {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .mobile-card {
        margin: 0 5px 25px 5px;
        border-radius: 20px;
    }

    .mobile-card-image {
        height: 200px;
    }

    .mobile-card-content {
        padding: 25px;
    }

    .mobile-card-header h4 {
        font-size: 1.25rem;
    }

    .mobile-card-info {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .info-item {
        justify-content: flex-start;
    }

    .filter-tabs-wrapper {
        padding: 0 20px;
        gap: 12px;
    }

    .mobile-filter-tab {
        min-width: 120px;
        padding: 16px 22px;
        font-size: 0.95rem;
        border-radius: 25px;
    }

    .mobile-filter-tab i {
        font-size: 0.9rem;
        margin-right: 8px;
    }

    .mobile-explore-btn {
        padding: 16px 28px;
        font-size: 1rem;
        border-radius: 30px;
    }

    .mobile-view-more {
        padding: 16px 32px;
        font-size: 1rem;
        max-width: 320px;
    }

    .mobile-rating {
        padding: 6px 10px;
        margin-left: 10px;
    }

    .rating-stars {
        font-size: 0.9rem;
    }

    .rating-number {
        font-size: 0.8rem;
    }
}

/* Hide scrollbar but keep functionality */
.filter-scroll-container::-webkit-scrollbar {
    display: none;
}

.filter-scroll-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
/*** Mobile-Friendly Destination Section End ***/

/*** Enhanced About & Services Pages Start ***/
/* Enhanced About Page Styles */
.about-image-container {
    position: relative;
}

.about-image-container img {
    transition: all 0.3s ease;
}

.about-image-container:hover img {
    transform: scale(1.02);
}

.experience-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.experience-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.experience-text {
    font-size: 0.9rem;
    margin-top: 5px;
}

.about-content h1 {
    color: #2c3e50;
    font-weight: 700;
    line-height: 1.3;
}

.about-content p {
    color: #6c757d;
    line-height: 1.7;
    font-size: 1.1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-item:hover {
    background: white;
    border-color: var(--bs-primary);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-item i {
    font-size: 1.5rem;
}

.feature-item h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 2px;
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.stats-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

.stats-icon {
    transition: all 0.3s ease;
}

.stats-card:hover .stats-icon i {
    transform: scale(1.2);
}

.stats-card h3 {
    font-weight: 700;
    margin-bottom: 10px;
}

/* Mission & Vision Cards */
.mission-card, .vision-card {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.mission-card:hover, .vision-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    text-align: center;
}

.mission-card h3, .vision-card h3 {
    color: #2c3e50;
    font-weight: 700;
}

.mission-card p, .vision-card p {
    color: #6c757d;
    line-height: 1.7;
}

/* Enhanced Services Page Styles */
.service-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

.service-icon-wrapper {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon-wrapper {
    transform: scale(1.1) rotate(5deg);
}

.service-icon {
    font-size: 2rem;
    color: white;
}

.service-content h4 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

.service-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.service-features li {
    padding: 5px 0;
    color: #495057;
    font-size: 0.9rem;
}

.service-price {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.price-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--bs-primary);
}

.service-card .btn {
    transition: all 0.3s ease;
}

.service-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

/* Process Steps */
.process-step {
    padding: 30px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px;
}

.process-step h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
}

.process-step p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
}
/*** Enhanced About & Services Pages End ***/

/*** Simple Packages Page Start ***/
/* Package filter buttons */
.package-filters .btn {
    margin: 0 5px 10px 5px;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.package-filters .btn.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.package-filters .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

/* Package cards */
.package-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--bs-primary);
}

.package-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.package-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.package-card:hover .package-image img {
    transform: scale(1.1);
}

.package-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.package-badge.budget {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.package-badge.luxury {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.package-badge.family {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
}

.package-badge.adventure {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.package-price {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.95);
    color: var(--bs-primary);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 1.2rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
}

.package-content {
    padding: 25px;
}

.package-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.package-header h4 {
    color: #2c3e50;
    font-weight: 700;
    margin: 0;
    flex: 1;
}

.package-rating {
    text-align: right;
    flex-shrink: 0;
    margin-left: 15px;
}

.stars {
    color: #ffc107;
    font-size: 1rem;
    display: block;
    line-height: 1;
}

.rating-text {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 600;
}

.package-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.package-details {
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #495057;
}

.detail-item i {
    width: 20px;
    margin-right: 10px;
}

.package-includes {
    margin-bottom: 25px;
}

.package-includes h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
}

.package-includes ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.package-includes li {
    padding: 4px 0;
    font-size: 0.9rem;
    color: #495057;
}

.package-includes i {
    margin-right: 8px;
    font-size: 0.8rem;
}

.package-actions {
    display: flex;
    gap: 10px;
}

.btn-book {
    flex: 1;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-book:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.btn-details {
    flex: 1;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-details:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .package-filters .btn {
        width: 100%;
        margin: 5px 0;
    }

    .package-card {
        margin-bottom: 30px;
    }

    .package-content {
        padding: 20px;
    }

    .package-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .package-rating {
        text-align: left;
        margin-left: 0;
        margin-top: 10px;
    }

    .package-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .package-image {
        height: 200px;
    }

    .package-content {
        padding: 15px;
    }

    .package-header h4 {
        font-size: 1.2rem;
    }
}
/*** Simple Packages Page End ***/

/*** Enhanced Booking Page Start ***/
/* Booking info section */
.booking-info {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.booking-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.booking-step:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5px;
}

.step-content p {
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    padding: 8px 0;
    color: #495057;
    font-weight: 500;
}

/* Booking form container */
.booking-form-container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.booking-form-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.booking-form-header h3 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Form sections */
.form-section {
    margin-bottom: 35px;
    padding-bottom: 25px;
    border-bottom: 1px solid #f1f3f4;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section .section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--bs-primary);
    display: inline-block;
}

/* Form controls */
.booking-form .form-control,
.booking-form .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.booking-form .form-control:focus,
.booking-form .form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.booking-form .form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

/* Form validation */
.booking-form .is-invalid {
    border-color: #dc3545;
}

.booking-form .invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 5px;
}

/* Form check */
.booking-form .form-check {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.booking-form .form-check:hover {
    border-color: var(--bs-primary);
}

.booking-form .form-check-input {
    margin-top: 0.25em;
}

.booking-form .form-check-label {
    color: #495057;
    font-weight: 500;
    line-height: 1.5;
}

/* Submit button */
.booking-form .btn-primary {
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border: none;
    border-radius: 10px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.booking-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .booking-form-container {
        padding: 25px;
    }

    .booking-info {
        padding: 20px;
        margin-bottom: 30px;
    }

    .booking-step {
        margin-bottom: 20px;
        padding-bottom: 20px;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .form-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
    }
}

@media (max-width: 576px) {
    .booking-form-container {
        padding: 20px;
    }

    .booking-form-header {
        margin-bottom: 30px;
    }

    .booking-form-header h3 {
        font-size: 1.5rem;
    }
}
/*** Enhanced Booking Page End ***/

/*** Enhanced Contact Page Start ***/
/* Contact info section */
.contact-info {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-details h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
}

.contact-details p {
    color: #6c757d;
    margin: 0;
    line-height: 1.6;
}

/* Social media */
.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    color: var(--bs-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.social-link:hover {
    background: var(--bs-primary);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

/* Contact form container */
.contact-form-container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.contact-form-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.contact-form-header h3 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Form sections */
.contact-form .form-section {
    margin-bottom: 35px;
    padding-bottom: 25px;
    border-bottom: 1px solid #f1f3f4;
}

.contact-form .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-form .form-section .section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--bs-primary);
    display: inline-block;
}

/* Form controls */
.contact-form .form-control,
.contact-form .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus,
.contact-form .form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.contact-form .form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

/* Form validation */
.contact-form .is-invalid {
    border-color: #dc3545;
}

.contact-form .invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 5px;
}

/* Submit button */
.contact-form .btn-primary {
    background: linear-gradient(135deg, var(--bs-primary), #0056b3);
    border: none;
    border-radius: 10px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .contact-form-container {
        padding: 25px;
    }

    .contact-info {
        padding: 20px;
        margin-bottom: 30px;
    }

    .contact-item {
        margin-bottom: 25px;
        padding-bottom: 20px;
    }

    .contact-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
        margin-right: 15px;
    }

    .social-links {
        gap: 10px;
    }

    .social-link {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 576px) {
    .contact-form-container {
        padding: 20px;
    }

    .contact-form-header {
        margin-bottom: 30px;
    }

    .contact-form-header h3 {
        font-size: 1.5rem;
    }
}
/*** Enhanced Contact Page End ***/