from flask import Flask, render_template_string, request, jsonify, redirect, url_for
from app import app, db, Contact, Booking, Newsletter
from datetime import datetime, timedelta
import json

# Simple Admin Dashboard
@app.route('/admin')
def admin_dashboard():
    """Simple admin dashboard"""
    
    # Get statistics
    total_contacts = Contact.query.count()
    total_bookings = Booking.query.count()
    total_subscribers = Newsletter.query.count()
    
    # Recent activity
    recent_contacts = Contact.query.order_by(Contact.created_at.desc()).limit(5).all()
    recent_bookings = Booking.query.order_by(Booking.created_at.desc()).limit(5).all()
    
    # Pending items
    pending_contacts = Contact.query.filter_by(status='new').count()
    pending_bookings = Booking.query.filter_by(status='pending').count()
    
    admin_template = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Travela Admin Dashboard</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            .dashboard-card {
                border-radius: 15px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }
            .dashboard-card:hover {
                transform: translateY(-5px);
            }
            .stat-icon {
                font-size: 2.5rem;
                opacity: 0.8;
            }
        </style>
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-dark bg-primary">
            <div class="container">
                <span class="navbar-brand mb-0 h1">
                    <i class="fa fa-plane me-2"></i>Travela Admin Dashboard
                </span>
                <a href="/" class="btn btn-outline-light">
                    <i class="fa fa-home me-2"></i>Back to Website
                </a>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-envelope stat-icon text-primary"></i>
                            <h3 class="mt-2">{{ total_contacts }}</h3>
                            <p class="text-muted">Total Contacts</p>
                            {% if pending_contacts > 0 %}
                            <span class="badge bg-warning">{{ pending_contacts }} New</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-calendar-check stat-icon text-success"></i>
                            <h3 class="mt-2">{{ total_bookings }}</h3>
                            <p class="text-muted">Total Bookings</p>
                            {% if pending_bookings > 0 %}
                            <span class="badge bg-warning">{{ pending_bookings }} Pending</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-users stat-icon text-info"></i>
                            <h3 class="mt-2">{{ total_subscribers }}</h3>
                            <p class="text-muted">Newsletter Subscribers</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card dashboard-card text-center p-3">
                        <div class="card-body">
                            <i class="fa fa-chart-line stat-icon text-warning"></i>
                            <h3 class="mt-2">{{ ((total_bookings / (total_contacts or 1)) * 100)|round(1) }}%</h3>
                            <p class="text-muted">Conversion Rate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <!-- Recent Contacts -->
                <div class="col-md-6">
                    <div class="card dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-envelope me-2"></i>Recent Contacts
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_contacts %}
                                {% for contact in recent_contacts %}
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <strong>{{ contact.first_name }} {{ contact.last_name }}</strong><br>
                                        <small class="text-muted">{{ contact.subject }}</small><br>
                                        <small class="text-muted">{{ contact.inquiry_type }}</small>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ contact.created_at.strftime('%m/%d %H:%M') }}</small><br>
                                        <span class="badge bg-{{ 'success' if contact.status == 'resolved' else 'warning' }}">
                                            {{ contact.status }}
                                        </span>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted">No contacts yet.</p>
                            {% endif %}
                            <div class="text-center mt-3">
                                <a href="/admin/contacts" class="btn btn-outline-primary btn-sm">View All Contacts</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="col-md-6">
                    <div class="card dashboard-card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-calendar-check me-2"></i>Recent Bookings
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if recent_bookings %}
                                {% for booking in recent_bookings %}
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <strong>{{ booking.first_name }} {{ booking.last_name }}</strong><br>
                                        <small class="text-muted">{{ booking.destination }}</small><br>
                                        <small class="text-primary">{{ booking.booking_id }}</small>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ booking.created_at.strftime('%m/%d %H:%M') }}</small><br>
                                        <span class="badge bg-{{ 'success' if booking.status == 'confirmed' else 'warning' }}">
                                            {{ booking.status }}
                                        </span>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted">No bookings yet.</p>
                            {% endif %}
                            <div class="text-center mt-3">
                                <a href="/admin/bookings" class="btn btn-outline-success btn-sm">View All Bookings</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="/admin/contacts" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="fa fa-envelope me-2"></i>Manage Contacts
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="/admin/bookings" class="btn btn-outline-success w-100 mb-2">
                                        <i class="fa fa-calendar me-2"></i>Manage Bookings
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="/admin/newsletter" class="btn btn-outline-info w-100 mb-2">
                                        <i class="fa fa-users me-2"></i>Newsletter
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="/admin/export" class="btn btn-outline-warning w-100 mb-2">
                                        <i class="fa fa-download me-2"></i>Export Data
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    return render_template_string(admin_template, 
                                total_contacts=total_contacts,
                                total_bookings=total_bookings,
                                total_subscribers=total_subscribers,
                                recent_contacts=recent_contacts,
                                recent_bookings=recent_bookings,
                                pending_contacts=pending_contacts,
                                pending_bookings=pending_bookings)

@app.route('/admin/contacts')
def admin_contacts():
    """View all contacts"""
    contacts = Contact.query.order_by(Contact.created_at.desc()).all()
    
    contacts_template = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Contacts - Travela Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-dark bg-primary">
            <div class="container">
                <span class="navbar-brand mb-0 h1">
                    <i class="fa fa-envelope me-2"></i>Contact Management
                </span>
                <a href="/admin" class="btn btn-outline-light">
                    <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Contact Submissions</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Type</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contact in contacts %}
                                <tr>
                                    <td>{{ contact.created_at.strftime('%m/%d/%Y %H:%M') }}</td>
                                    <td>{{ contact.first_name }} {{ contact.last_name }}</td>
                                    <td>{{ contact.email }}</td>
                                    <td>{{ contact.inquiry_type }}</td>
                                    <td>{{ contact.subject }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if contact.status == 'resolved' else 'warning' }}">
                                            {{ contact.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewContact({{ contact.id }})">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function viewContact(id) {
                // Implement contact details view
                alert('Contact details view - ID: ' + id);
            }
        </script>
    </body>
    </html>
    """
    
    return render_template_string(contacts_template, contacts=contacts)

@app.route('/admin/bookings')
def admin_bookings():
    """View all bookings"""
    bookings = Booking.query.order_by(Booking.created_at.desc()).all()
    
    bookings_template = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bookings - Travela Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </head>
    <body class="bg-light">
        <nav class="navbar navbar-dark bg-primary">
            <div class="container">
                <span class="navbar-brand mb-0 h1">
                    <i class="fa fa-calendar-check me-2"></i>Booking Management
                </span>
                <a href="/admin" class="btn btn-outline-light">
                    <i class="fa fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Booking Requests</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Destination</th>
                                    <th>Package</th>
                                    <th>Departure</th>
                                    <th>Travelers</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in bookings %}
                                <tr>
                                    <td><strong>{{ booking.booking_id }}</strong></td>
                                    <td>{{ booking.created_at.strftime('%m/%d/%Y') }}</td>
                                    <td>{{ booking.first_name }} {{ booking.last_name }}</td>
                                    <td>{{ booking.destination }}</td>
                                    <td>{{ booking.package_type }}</td>
                                    <td>{{ booking.departure_date.strftime('%m/%d/%Y') }}</td>
                                    <td>{{ booking.adults }}A{% if booking.children > 0 %}, {{ booking.children }}C{% endif %}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if booking.status == 'confirmed' else 'warning' }}">
                                            {{ booking.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewBooking('{{ booking.booking_id }}')">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            function viewBooking(bookingId) {
                // Implement booking details view
                alert('Booking details view - ID: ' + bookingId);
            }
        </script>
    </body>
    </html>
    """
    
    return render_template_string(bookings_template, bookings=bookings)
