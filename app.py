from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail, Message
from datetime import datetime
import os
from werkzeug.security import generate_password_hash
import uuid

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'your-secret-key-change-this-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///tourism.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Email configuration (Gmail SMTP)
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # Change this
app.config['MAIL_PASSWORD'] = 'your-app-password'     # Change this
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'

# Initialize extensions
db = SQLAlchemy(app)
mail = Mail(app)

# Database Models
class Contact(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    inquiry_type = db.Column(db.String(50), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20))
    subject = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    preferred_contact = db.Column(db.String(20), default='email')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='new')

    def __repr__(self):
        return f'<Contact {self.first_name} {self.last_name}>'

class Booking(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    booking_id = db.Column(db.String(20), unique=True, nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    destination = db.Column(db.String(100), nullable=False)
    package_type = db.Column(db.String(50), nullable=False)
    departure_date = db.Column(db.Date, nullable=False)
    return_date = db.Column(db.Date)
    adults = db.Column(db.Integer, nullable=False)
    children = db.Column(db.Integer, default=0)
    special_requests = db.Column(db.Text)
    newsletter = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='pending')

    def __repr__(self):
        return f'<Booking {self.booking_id}>'

class Newsletter(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    active = db.Column(db.Boolean, default=True)

    def __repr__(self):
        return f'<Newsletter {self.email}>'

# Helper Functions
def generate_booking_id():
    """Generate unique booking ID"""
    return f"TRV{datetime.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:6].upper()}"

def send_email(to, subject, template, **kwargs):
    """Send email using template"""
    try:
        msg = Message(
            subject=subject,
            recipients=[to],
            html=template,
            sender=app.config['MAIL_DEFAULT_SENDER']
        )
        mail.send(msg)
        return True
    except Exception as e:
        print(f"Email sending failed: {e}")
        return False

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/about')
def about():
    return render_template('about.html')

@app.route('/services')
def services():
    return render_template('services.html')

@app.route('/packages')
def packages():
    return render_template('packages.html')

@app.route('/booking')
def booking():
    return render_template('booking.html')

@app.route('/contact')
def contact():
    return render_template('contact.html')

@app.route('/tour')
def tour():
    return render_template('tour.html')

# API Routes
@app.route('/api/contact', methods=['POST'])
def handle_contact():
    """Handle contact form submission"""
    try:
        data = request.get_json()
        
        # Create new contact entry
        contact = Contact(
            inquiry_type=data.get('inquiryType'),
            first_name=data.get('firstName'),
            last_name=data.get('lastName'),
            email=data.get('email'),
            phone=data.get('phone', ''),
            subject=data.get('subject'),
            message=data.get('message'),
            preferred_contact=data.get('preferredContact', 'email')
        )
        
        db.session.add(contact)
        db.session.commit()
        
        # Send notification email to business
        business_email = "<EMAIL>"  # Change this
        business_template = f"""
        <h2>New Contact Form Submission</h2>
        <p><strong>Type:</strong> {data.get('inquiryType')}</p>
        <p><strong>Name:</strong> {data.get('firstName')} {data.get('lastName')}</p>
        <p><strong>Email:</strong> {data.get('email')}</p>
        <p><strong>Phone:</strong> {data.get('phone', 'Not provided')}</p>
        <p><strong>Subject:</strong> {data.get('subject')}</p>
        <p><strong>Message:</strong></p>
        <p>{data.get('message')}</p>
        <p><strong>Preferred Contact:</strong> {data.get('preferredContact', 'email')}</p>
        """
        
        send_email(business_email, f"New Contact: {data.get('subject')}", business_template)
        
        # Send auto-reply to customer
        customer_template = f"""
        <h2>Thank you for contacting Travela!</h2>
        <p>Dear {data.get('firstName')},</p>
        <p>We have received your message and will get back to you within 2-4 hours during business hours.</p>
        <p><strong>Your message:</strong></p>
        <p>{data.get('message')}</p>
        <p>Best regards,<br>Travela Team</p>
        """
        
        send_email(data.get('email'), "Thank you for contacting Travela", customer_template)
        
        return jsonify({'success': True, 'message': 'Message sent successfully!'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@app.route('/api/booking', methods=['POST'])
def handle_booking():
    """Handle booking form submission"""
    try:
        data = request.get_json()
        
        # Generate unique booking ID
        booking_id = generate_booking_id()
        
        # Create new booking entry
        booking = Booking(
            booking_id=booking_id,
            first_name=data.get('firstName'),
            last_name=data.get('lastName'),
            email=data.get('email'),
            phone=data.get('phone'),
            destination=data.get('destination'),
            package_type=data.get('packageType'),
            departure_date=datetime.strptime(data.get('departureDate'), '%Y-%m-%d').date(),
            return_date=datetime.strptime(data.get('returnDate'), '%Y-%m-%d').date() if data.get('returnDate') else None,
            adults=int(data.get('adults')),
            children=int(data.get('children', 0)),
            special_requests=data.get('specialRequests', ''),
            newsletter=data.get('newsletter', False)
        )
        
        db.session.add(booking)
        
        # Add to newsletter if requested
        if data.get('newsletter'):
            existing_subscriber = Newsletter.query.filter_by(email=data.get('email')).first()
            if not existing_subscriber:
                newsletter = Newsletter(email=data.get('email'))
                db.session.add(newsletter)
        
        db.session.commit()
        
        # Send booking confirmation to customer
        customer_template = f"""
        <h2>Booking Confirmation - Travela</h2>
        <p>Dear {data.get('firstName')},</p>
        <p>Thank you for your booking request! Here are your booking details:</p>
        <p><strong>Booking ID:</strong> {booking_id}</p>
        <p><strong>Destination:</strong> {data.get('destination')}</p>
        <p><strong>Package:</strong> {data.get('packageType')}</p>
        <p><strong>Departure Date:</strong> {data.get('departureDate')}</p>
        <p><strong>Travelers:</strong> {data.get('adults')} Adults, {data.get('children', 0)} Children</p>
        <p>We will contact you within 24 hours to confirm your booking and provide payment details.</p>
        <p>Best regards,<br>Travela Team</p>
        """
        
        send_email(data.get('email'), f"Booking Confirmation - {booking_id}", customer_template)
        
        # Send notification to business
        business_email = "<EMAIL>"  # Change this
        business_template = f"""
        <h2>New Booking Request</h2>
        <p><strong>Booking ID:</strong> {booking_id}</p>
        <p><strong>Customer:</strong> {data.get('firstName')} {data.get('lastName')}</p>
        <p><strong>Email:</strong> {data.get('email')}</p>
        <p><strong>Phone:</strong> {data.get('phone')}</p>
        <p><strong>Destination:</strong> {data.get('destination')}</p>
        <p><strong>Package:</strong> {data.get('packageType')}</p>
        <p><strong>Departure:</strong> {data.get('departureDate')}</p>
        <p><strong>Return:</strong> {data.get('returnDate', 'Not specified')}</p>
        <p><strong>Travelers:</strong> {data.get('adults')} Adults, {data.get('children', 0)} Children</p>
        <p><strong>Special Requests:</strong> {data.get('specialRequests', 'None')}</p>
        """
        
        send_email(business_email, f"New Booking: {booking_id}", business_template)
        
        return jsonify({
            'success': True, 
            'message': 'Booking request submitted successfully!',
            'booking_id': booking_id
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

# Initialize database
def create_tables():
    with app.app_context():
        db.create_all()

if __name__ == '__main__':
    create_tables()
    # Import admin routes
    import admin
    app.run(debug=True, host='0.0.0.0', port=5000)
