# 🌟 Travela Tourism Website - Complete Full-Stack Solution

A modern, responsive tourism website with Python Flask backend, featuring booking system, contact forms, and admin dashboard.

## ✨ Features

### 🎨 Frontend Features
- **Responsive Design** - Works perfectly on all devices
- **Modern UI/UX** - Clean, professional design with smooth animations
- **Package Filtering** - Easy-to-use package filtering system
- **Interactive Forms** - Real-time validation and user feedback
- **Mobile Optimized** - Touch-friendly navigation and layouts

### 🔧 Backend Features
- **Contact Form Processing** - Automated email handling
- **Booking System** - Complete booking management with confirmations
- **Database Storage** - SQLite database for all data
- **Email Integration** - Automated emails for confirmations and notifications
- **Admin Dashboard** - Manage bookings, contacts, and view analytics
- **API Endpoints** - RESTful API for form submissions

## 🚀 Quick Start

### 1. Prerequisites
- Python 3.7 or higher
- Gmail account (for email functionality)

### 2. Installation

```bash
# Clone or download the project
cd tourism-website-template

# Run the setup script
python setup.py
```

### 3. Configure Email
Edit the `.env` file with your email credentials:

```env
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
BUSINESS_EMAIL=<EMAIL>
```

**Important**: Use Gmail App Password, not your regular password!

### 4. Start the Servers

```bash
# Start backend server (Terminal 1)
python run_backend.py

# Start frontend server (Terminal 2) 
python -m http.server 8000
```

### 5. Access Your Website
- **Frontend**: http://localhost:8000
- **Backend API**: http://localhost:5000
- **Admin Panel**: http://localhost:5000/admin

## 📧 Email Setup Guide

### Gmail App Password Setup:
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Security → 2-Step Verification (enable if not already)
3. Security → App passwords
4. Generate password for "Mail"
5. Use this password in `.env` file

## 📁 Project Structure

```
tourism-website-template/
├── 🎨 Frontend Files
│   ├── index.html          # Homepage
│   ├── about.html          # About page
│   ├── services.html       # Services page
│   ├── packages.html       # Packages page
│   ├── booking.html        # Booking page
│   ├── contact.html        # Contact page
│   ├── css/style.css       # Enhanced styles
│   └── js/backend.js       # Backend integration
│
├── 🔧 Backend Files
│   ├── app.py              # Main Flask application
│   ├── admin.py            # Admin dashboard
│   ├── requirements.txt    # Python dependencies
│   ├── setup.py           # Setup script
│   └── run_backend.py     # Server runner
│
├── 📄 Configuration
│   ├── .env.example       # Environment template
│   └── README.md          # This file
│
└── 💾 Database
    └── tourism.db         # SQLite database (auto-created)
```

## 🎯 API Endpoints

### Contact Form
```
POST /api/contact
Content-Type: application/json

{
  "inquiryType": "booking",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "subject": "Travel Inquiry",
  "message": "I'm interested in...",
  "preferredContact": "email"
}
```

### Booking Form
```
POST /api/booking
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "destination": "paris",
  "packageType": "luxury",
  "departureDate": "2024-06-15",
  "returnDate": "2024-06-22",
  "adults": "2",
  "children": "0",
  "specialRequests": "Vegetarian meals",
  "newsletter": true,
  "terms": true
}
```

## 👨‍💼 Admin Dashboard

Access the admin dashboard at `/admin` to:

- **View Statistics** - Total contacts, bookings, conversion rates
- **Manage Contacts** - View and respond to customer inquiries
- **Manage Bookings** - Track and update booking requests
- **Newsletter Management** - View subscriber list
- **Export Data** - Download customer data

## 🎨 Customization

### Update Business Information
Edit these files with your business details:
- `.env` - Email and contact information
- `app.py` - Business email addresses
- HTML files - Company name, address, phone numbers

### Styling
- `css/style.css` - All custom styles
- Modify colors, fonts, and layouts as needed

### Add New Features
- Add new routes in `app.py`
- Create new database models
- Extend the admin dashboard

## 🔒 Security Features

- **Form Validation** - Client and server-side validation
- **SQL Injection Protection** - SQLAlchemy ORM
- **Email Security** - Secure SMTP with TLS
- **Environment Variables** - Sensitive data in .env file

## 📱 Mobile Optimization

- **Responsive Grid** - Bootstrap-based responsive design
- **Touch-Friendly** - Large buttons and proper spacing
- **Fast Loading** - Optimized images and minimal JavaScript
- **Mobile Navigation** - Collapsible menu and touch gestures

## 🚀 Deployment Options

### Local Development
```bash
python run_backend.py
```

### Production Deployment
1. **Heroku** - Easy deployment with git
2. **DigitalOcean** - VPS with more control
3. **AWS** - Scalable cloud hosting
4. **PythonAnywhere** - Simple Python hosting

### Environment Variables for Production
```env
FLASK_ENV=production
SECRET_KEY=your-secure-secret-key
DATABASE_URL=postgresql://user:pass@host:port/db
```

## 🧪 Testing

### Test Contact Form
1. Go to `/contact`
2. Fill out the form
3. Check email for confirmation
4. Check `/admin` for submission

### Test Booking Form
1. Go to `/booking`
2. Fill out booking details
3. Check email for booking confirmation
4. Check `/admin/bookings` for new booking

## 🆘 Troubleshooting

### Email Not Working
- Check Gmail App Password setup
- Verify `.env` file configuration
- Check spam folder for emails
- Enable "Less secure app access" if needed

### Database Issues
- Delete `tourism.db` and restart
- Check file permissions
- Run `python setup.py` again

### Port Already in Use
```bash
# Kill process on port 5000
lsof -ti:5000 | xargs kill -9

# Or use different port
python app.py --port 5001
```

## 📞 Support

For questions or issues:
1. Check this README
2. Review error messages in terminal
3. Check browser console for JavaScript errors
4. Verify email configuration

## 🎉 Success!

Your tourism website is now fully functional with:
- ✅ Beautiful, responsive frontend
- ✅ Working contact and booking forms
- ✅ Email notifications and confirmations
- ✅ Admin dashboard for management
- ✅ Database storage for all data
- ✅ Mobile-optimized experience

**Happy travels! 🌍✈️**
