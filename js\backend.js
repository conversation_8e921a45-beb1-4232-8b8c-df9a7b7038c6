// Backend Integration JavaScript
// This file handles form submissions to the Python Flask backend

// Contact Form Handler
document.addEventListener('DOMContentLoaded', function() {
    // Contact Form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactSubmission);
    }

    // Booking Form
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', handleBookingSubmission);
    }
});

// Handle Contact Form Submission
async function handleContactSubmission(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Sending...';
    submitButton.disabled = true;
    
    // Collect form data
    const formData = {
        inquiryType: form.inquiryType.value,
        firstName: form.firstName.value,
        lastName: form.lastName.value,
        email: form.email.value,
        phone: form.phone.value,
        subject: form.subject.value,
        message: form.message.value,
        preferredContact: form.preferredContact.value
    };
    
    try {
        const response = await fetch('/api/contact', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Message sent successfully! We will get back to you soon.', 'success');
            form.reset();
        } else {
            showNotification('Error sending message: ' + result.message, 'error');
        }
        
    } catch (error) {
        console.error('Error:', error);
        showNotification('Network error. Please try again later.', 'error');
    } finally {
        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }
}

// Handle Booking Form Submission
async function handleBookingSubmission(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Processing...';
    submitButton.disabled = true;
    
    // Collect form data
    const formData = {
        firstName: form.firstName.value,
        lastName: form.lastName.value,
        email: form.email.value,
        phone: form.phone.value,
        destination: form.destination.value,
        packageType: form.packageType.value,
        departureDate: form.departureDate.value,
        returnDate: form.returnDate.value,
        adults: form.adults.value,
        children: form.children.value,
        specialRequests: form.specialRequests.value,
        newsletter: form.newsletter.checked,
        terms: form.terms.checked
    };
    
    // Validate required fields
    if (!formData.terms) {
        showNotification('Please accept the terms and conditions.', 'error');
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
        return;
    }
    
    try {
        const response = await fetch('/api/booking', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification(
                `Booking request submitted successfully! Your booking ID is: ${result.booking_id}. Check your email for confirmation.`, 
                'success'
            );
            form.reset();
            
            // Optionally redirect to a thank you page
            setTimeout(() => {
                // window.location.href = '/booking-confirmation?id=' + result.booking_id;
            }, 3000);
        } else {
            showNotification('Error submitting booking: ' + result.message, 'error');
        }
        
    } catch (error) {
        console.error('Error:', error);
        showNotification('Network error. Please try again later.', 'error');
    } finally {
        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }
}

// Show notification to user
function showNotification(message, type) {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px; 
        right: 20px; 
        z-index: 9999; 
        min-width: 350px;
        max-width: 500px;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    `;
    
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    notification.innerHTML = `
        <i class="fa ${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Form validation helpers
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Real-time form validation
document.addEventListener('DOMContentLoaded', function() {
    // Add real-time validation to email fields
    const emailFields = document.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value && !validateEmail(this.value)) {
                this.classList.add('is-invalid');
                showFieldError(this, 'Please enter a valid email address');
            } else {
                this.classList.remove('is-invalid');
                hideFieldError(this);
            }
        });
    });
    
    // Add real-time validation to phone fields
    const phoneFields = document.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (this.value && !validatePhone(this.value)) {
                this.classList.add('is-invalid');
                showFieldError(this, 'Please enter a valid phone number');
            } else {
                this.classList.remove('is-invalid');
                hideFieldError(this);
            }
        });
    });
    
    // Date validation
    const dateFields = document.querySelectorAll('input[type="date"]');
    dateFields.forEach(field => {
        field.addEventListener('change', function() {
            const selectedDate = new Date(this.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                this.classList.add('is-invalid');
                showFieldError(this, 'Please select a future date');
            } else {
                this.classList.remove('is-invalid');
                hideFieldError(this);
            }
        });
    });
});

function showFieldError(field, message) {
    hideFieldError(field);
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

function hideFieldError(field) {
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
}
