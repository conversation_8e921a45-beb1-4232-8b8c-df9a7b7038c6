#!/usr/bin/env python3
"""
Simple script to view backend data from your tourism website
This script shows all contact forms, bookings, and newsletter subscriptions
"""

import sqlite3
import os
from datetime import datetime

def print_header(title):
    print("=" * 60)
    print(f"📊 {title}")
    print("=" * 60)

def view_contacts():
    """View all contact form submissions"""
    print_header("CONTACT FORM SUBMISSIONS")
    
    if not os.path.exists('tourism.db'):
        print("❌ Database not found! Please run the Flask app first to create the database.")
        return
    
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT id, inquiry_type, first_name, last_name, email, phone, 
                   subject, message, preferred_contact, created_at, status
            FROM contact 
            ORDER BY created_at DESC
        """)
        
        contacts = cursor.fetchall()
        
        if not contacts:
            print("📝 No contact form submissions yet.")
            print("💡 Submit a contact form at: http://localhost:8000/contact.html")
        else:
            print(f"📊 Total Contact Submissions: {len(contacts)}")
            print()
            
            for contact in contacts:
                print(f"🆔 ID: {contact[0]}")
                print(f"📋 Type: {contact[1]}")
                print(f"👤 Name: {contact[2]} {contact[3]}")
                print(f"📧 Email: {contact[4]}")
                print(f"📞 Phone: {contact[5] or 'Not provided'}")
                print(f"📝 Subject: {contact[6]}")
                print(f"💬 Message: {contact[7]}")
                print(f"📞 Preferred Contact: {contact[8]}")
                print(f"📅 Date: {contact[9]}")
                print(f"🔄 Status: {contact[10]}")
                print("-" * 50)
                
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    finally:
        conn.close()

def view_bookings():
    """View all booking requests"""
    print_header("BOOKING REQUESTS")
    
    if not os.path.exists('tourism.db'):
        print("❌ Database not found! Please run the Flask app first to create the database.")
        return
    
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT booking_id, first_name, last_name, email, phone, destination, 
                   package_type, departure_date, return_date, adults, children, 
                   special_requests, newsletter, created_at, status
            FROM booking 
            ORDER BY created_at DESC
        """)
        
        bookings = cursor.fetchall()
        
        if not bookings:
            print("🎫 No booking requests yet.")
            print("💡 Submit a booking at: http://localhost:8000/booking.html")
        else:
            print(f"📊 Total Booking Requests: {len(bookings)}")
            print()
            
            for booking in bookings:
                print(f"🎫 Booking ID: {booking[0]}")
                print(f"👤 Customer: {booking[1]} {booking[2]}")
                print(f"📧 Email: {booking[3]}")
                print(f"📞 Phone: {booking[4]}")
                print(f"🌍 Destination: {booking[5]}")
                print(f"📦 Package: {booking[6]}")
                print(f"✈️ Departure: {booking[7]}")
                print(f"🔄 Return: {booking[8] or 'Not specified'}")
                print(f"👥 Travelers: {booking[9]} Adults, {booking[10]} Children")
                print(f"📝 Special Requests: {booking[11] or 'None'}")
                print(f"📧 Newsletter: {'Yes' if booking[12] else 'No'}")
                print(f"📅 Booking Date: {booking[13]}")
                print(f"🔄 Status: {booking[14]}")
                print("-" * 50)
                
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    finally:
        conn.close()

def view_newsletter():
    """View newsletter subscribers"""
    print_header("NEWSLETTER SUBSCRIBERS")
    
    if not os.path.exists('tourism.db'):
        print("❌ Database not found! Please run the Flask app first to create the database.")
        return
    
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT id, email, created_at, active
            FROM newsletter 
            ORDER BY created_at DESC
        """)
        
        subscribers = cursor.fetchall()
        
        if not subscribers:
            print("📧 No newsletter subscribers yet.")
        else:
            print(f"📊 Total Newsletter Subscribers: {len(subscribers)}")
            print()
            
            for subscriber in subscribers:
                print(f"🆔 ID: {subscriber[0]}")
                print(f"📧 Email: {subscriber[1]}")
                print(f"📅 Subscribed: {subscriber[2]}")
                print(f"✅ Active: {'Yes' if subscriber[3] else 'No'}")
                print("-" * 30)
                
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    finally:
        conn.close()

def view_statistics():
    """View overall statistics"""
    print_header("WEBSITE STATISTICS")
    
    if not os.path.exists('tourism.db'):
        print("❌ Database not found! Please run the Flask app first to create the database.")
        return
    
    conn = sqlite3.connect('tourism.db')
    cursor = conn.cursor()
    
    try:
        # Get counts
        cursor.execute("SELECT COUNT(*) FROM contact")
        total_contacts = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM booking")
        total_bookings = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM newsletter")
        total_subscribers = cursor.fetchone()[0]
        
        # Calculate conversion rate
        conversion_rate = (total_bookings / total_contacts * 100) if total_contacts > 0 else 0
        
        print(f"📊 Total Contacts: {total_contacts}")
        print(f"🎫 Total Bookings: {total_bookings}")
        print(f"📧 Newsletter Subscribers: {total_subscribers}")
        print(f"📈 Conversion Rate: {conversion_rate:.1f}%")
        print()
        
        # Recent activity
        cursor.execute("SELECT created_at FROM contact ORDER BY created_at DESC LIMIT 1")
        last_contact = cursor.fetchone()
        
        cursor.execute("SELECT created_at FROM booking ORDER BY created_at DESC LIMIT 1")
        last_booking = cursor.fetchone()
        
        if last_contact:
            print(f"📅 Last Contact: {last_contact[0]}")
        if last_booking:
            print(f"📅 Last Booking: {last_booking[0]}")
            
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    finally:
        conn.close()

def main():
    """Main function"""
    print("🌟 TRAVELA TOURISM WEBSITE - DATA VIEWER")
    print("📊 View all your backend data from contact forms and bookings")
    print()
    
    while True:
        print("\n📋 What would you like to view?")
        print("1. 📊 Statistics Overview")
        print("2. 📝 Contact Form Submissions")
        print("3. 🎫 Booking Requests")
        print("4. 📧 Newsletter Subscribers")
        print("5. 🔄 View All Data")
        print("6. ❌ Exit")
        
        choice = input("\n👉 Enter your choice (1-6): ").strip()
        
        if choice == '1':
            view_statistics()
        elif choice == '2':
            view_contacts()
        elif choice == '3':
            view_bookings()
        elif choice == '4':
            view_newsletter()
        elif choice == '5':
            view_statistics()
            view_contacts()
            view_bookings()
            view_newsletter()
        elif choice == '6':
            print("\n👋 Thank you for using Travela Data Viewer!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-6.")
        
        input("\n⏸️  Press Enter to continue...")

if __name__ == "__main__":
    main()
