<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8">
        <title>Book Your Tour - Travela Tourism</title>
        <meta content="width=device-width, initial-scale=1.0" name="viewport">
        <meta content="book tour, travel booking, tour deals, vacation packages, travel reservations" name="keywords">
        <meta content="Book your dream vacation with <PERSON><PERSON>. Get 50% off on your first adventure trip. Easy online booking for tour packages and travel deals." name="description">

        <!-- Google Web Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Jost:wght@500;600&family=Roboto&display=swap" rel="stylesheet"> 

        <!-- Icon Font Stylesheet -->
        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css"/>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

        <!-- Libraries Stylesheet -->
        <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
        <link href="lib/lightbox/css/lightbox.min.css" rel="stylesheet">


        <!-- Customized Bootstrap Stylesheet -->
        <link href="css/bootstrap.min.css" rel="stylesheet">

        <!-- Template Stylesheet -->
        <link href="css/style.css" rel="stylesheet">
    </head>

    <body>

        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Topbar Start -->
        <div class="container-fluid bg-primary px-5 d-none d-lg-block">
            <div class="row gx-0">
                <div class="col-lg-8 text-center text-lg-start mb-2 mb-lg-0">
                    <div class="d-inline-flex align-items-center" style="height: 45px;">
                        <a class="btn btn-sm btn-outline-light btn-sm-square rounded-circle me-2" href="https://twitter.com/travela" target="_blank"><i class="fab fa-twitter fw-normal"></i></a>
                        <a class="btn btn-sm btn-outline-light btn-sm-square rounded-circle me-2" href="https://facebook.com/travela" target="_blank"><i class="fab fa-facebook-f fw-normal"></i></a>
                        <a class="btn btn-sm btn-outline-light btn-sm-square rounded-circle me-2" href="https://linkedin.com/company/travela" target="_blank"><i class="fab fa-linkedin-in fw-normal"></i></a>
                        <a class="btn btn-sm btn-outline-light btn-sm-square rounded-circle me-2" href="https://instagram.com/travela" target="_blank"><i class="fab fa-instagram fw-normal"></i></a>
                        <a class="btn btn-sm btn-outline-light btn-sm-square rounded-circle" href="https://youtube.com/travela" target="_blank"><i class="fab fa-youtube fw-normal"></i></a>
                    </div>
                </div>
                <div class="col-lg-4 text-center text-lg-end">
                    <div class="d-inline-flex align-items-center" style="height: 45px;">
                        <a href="#"><small class="me-3 text-light"><i class="fa fa-user me-2"></i>Register</small></a>
                        <a href="#"><small class="me-3 text-light"><i class="fa fa-sign-in-alt me-2"></i>Login</small></a>
                        <div class="dropdown">
                            <a href="#" class="dropdown-toggle text-light" data-bs-toggle="dropdown"><small><i class="fa fa-home me-2"></i> My Dashboard</small></a>
                            <div class="dropdown-menu rounded">
                                <a href="#" class="dropdown-item"><i class="fas fa-user-alt me-2"></i> My Profile</a>
                                <a href="#" class="dropdown-item"><i class="fas fa-comment-alt me-2"></i> Inbox</a>
                                <a href="#" class="dropdown-item"><i class="fas fa-bell me-2"></i> Notifications</a>
                                <a href="#" class="dropdown-item"><i class="fas fa-cog me-2"></i> Account Settings</a>
                                <a href="#" class="dropdown-item"><i class="fas fa-power-off me-2"></i> Log Out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Topbar End -->

        <!-- Navbar & Hero Start -->
        <div class="container-fluid position-relative p-0">
            <nav class="navbar navbar-expand-lg navbar-light px-4 px-lg-5 py-3 py-lg-0">
                <a href="index.html" class="navbar-brand p-0">
                    <h1 class="m-0"><i class="fa fa-map-marker-alt me-3"></i>Travela</h1>
                    <!-- <img src="img/logo.png" alt="Logo"> -->
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
                    <span class="fa fa-bars"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarCollapse">
                    <div class="navbar-nav ms-auto py-0">
                        <a href="index.html" class="nav-item nav-link">Home</a>
                        <a href="about.html" class="nav-item nav-link">About</a>
                        <a href="services.html" class="nav-item nav-link">Services</a>
                        <a href="packages.html" class="nav-item nav-link">Packages</a>
                        <a href="blog.html" class="nav-item nav-link">Blog</a>
                        <div class="nav-item dropdown">
                            <a href="#" class="nav-link dropdown-toggle active" data-bs-toggle="dropdown">Pages</a>
                            <div class="dropdown-menu m-0">
                                <a href="destination.html" class="dropdown-item">Destination</a>
                                <a href="tour.html" class="dropdown-item">Explore Tour</a>
                                <a href="booking.html" class="dropdown-item active">Travel Booking</a>
                                <a href="gallery.html" class="dropdown-item">Our Gallery</a>
                                <a href="guides.html" class="dropdown-item">Travel Guides</a>
                                <a href="testimonial.html" class="dropdown-item">Testimonial</a>
                                <a href="404.html" class="dropdown-item">404 Page</a>
                            </div>
                        </div>
                        <a href="contact.html" class="nav-item nav-link">Contact</a>
                    </div>
                    <a href="booking.html" class="btn btn-primary rounded-pill py-2 px-4 ms-lg-4">Book Now</a>
                </div>
            </nav>
        </div>
        <!-- Navbar & Hero End -->

        <!-- Header Start -->
        <div class="container-fluid bg-breadcrumb">
            <div class="container text-center py-5" style="max-width: 900px;">
                <h3 class="text-white display-3 mb-4">Online Booking</h1>
                <ol class="breadcrumb justify-content-center mb-0">
                    <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                    <li class="breadcrumb-item"><a href="#">Pages</a></li>
                    <li class="breadcrumb-item active text-white">Online Booking</li>
                </ol>    
            </div>
        </div>
        <!-- Header End -->

        <!-- Enhanced Booking Start -->
        <div class="container-fluid py-5 bg-light">
            <div class="container">
                <!-- Header Section -->
                <div class="text-center mb-5">
                    <h5 class="section-title px-3">Book Your Trip</h5>
                    <h1 class="mb-4">Easy Online Booking</h1>
                    <p class="text-muted">Book your dream vacation in just a few simple steps</p>
                </div>

                <div class="row g-5">
                    <!-- Booking Steps -->
                    <div class="col-lg-4">
                        <div class="booking-info">
                            <h3 class="mb-4">How to Book</h3>

                            <div class="booking-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h5>Choose Your Destination</h5>
                                    <p>Select from our amazing destinations worldwide</p>
                                </div>
                            </div>

                            <div class="booking-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h5>Fill Your Details</h5>
                                    <p>Provide your travel information and preferences</p>
                                </div>
                            </div>

                            <div class="booking-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h5>Confirm & Pay</h5>
                                    <p>Review your booking and make secure payment</p>
                                </div>
                            </div>

                            <div class="booking-benefits mt-4">
                                <h5 class="mb-3">Why Book With Us?</h5>
                                <ul class="benefits-list">
                                    <li><i class="fa fa-check text-success me-2"></i>Best Price Guarantee</li>
                                    <li><i class="fa fa-check text-success me-2"></i>24/7 Customer Support</li>
                                    <li><i class="fa fa-check text-success me-2"></i>Free Cancellation</li>
                                    <li><i class="fa fa-check text-success me-2"></i>Instant Confirmation</li>
                                    <li><i class="fa fa-check text-success me-2"></i>Secure Payment</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Form -->
                    <div class="col-lg-8">
                        <div class="booking-form-container">
                            <div class="booking-form-header">
                                <h3>Book Your Adventure</h3>
                                <p class="text-muted">Fill in the details below to start your journey</p>
                            </div>
                            <form id="bookingForm" class="booking-form" novalidate>
                                <!-- Personal Information -->
                                <div class="form-section">
                                    <h5 class="section-title">Personal Information</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="firstName" placeholder="First Name" required>
                                                <label for="firstName">First Name *</label>
                                                <div class="invalid-feedback">Please provide your first name.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="lastName" placeholder="Last Name" required>
                                                <label for="lastName">Last Name *</label>
                                                <div class="invalid-feedback">Please provide your last name.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="email" class="form-control" id="email" placeholder="Email Address" required>
                                                <label for="email">Email Address *</label>
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="tel" class="form-control" id="phone" placeholder="Phone Number" required>
                                                <label for="phone">Phone Number *</label>
                                                <div class="invalid-feedback">Please provide your phone number.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Travel Details -->
                                <div class="form-section">
                                    <h5 class="section-title">Travel Details</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <select class="form-select" id="destination" required>
                                                    <option value="">Choose Destination</option>
                                                    <option value="new-york">New York City, USA</option>
                                                    <option value="paris">Paris, France</option>
                                                    <option value="tokyo">Tokyo, Japan</option>
                                                    <option value="london">London, UK</option>
                                                    <option value="dubai">Dubai, UAE</option>
                                                    <option value="sydney">Sydney, Australia</option>
                                                    <option value="maldives">Maldives</option>
                                                    <option value="thailand">Thailand</option>
                                                </select>
                                                <label for="destination">Destination *</label>
                                                <div class="invalid-feedback">Please select a destination.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <select class="form-select" id="packageType" required>
                                                    <option value="">Package Type</option>
                                                    <option value="budget">Budget Friendly ($300-$600)</option>
                                                    <option value="standard">Standard ($600-$1200)</option>
                                                    <option value="luxury">Luxury ($1200+)</option>
                                                    <option value="family">Family Package</option>
                                                    <option value="adventure">Adventure Package</option>
                                                    <option value="romantic">Romantic Getaway</option>
                                                </select>
                                                <label for="packageType">Package Type *</label>
                                                <div class="invalid-feedback">Please select a package type.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="date" class="form-control" id="departureDate" required>
                                                <label for="departureDate">Departure Date *</label>
                                                <div class="invalid-feedback">Please select a departure date.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <input type="date" class="form-control" id="returnDate">
                                                <label for="returnDate">Return Date (Optional)</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <select class="form-select" id="adults" required>
                                                    <option value="">Number of Adults</option>
                                                    <option value="1">1 Adult</option>
                                                    <option value="2">2 Adults</option>
                                                    <option value="3">3 Adults</option>
                                                    <option value="4">4 Adults</option>
                                                    <option value="5">5+ Adults</option>
                                                </select>
                                                <label for="adults">Adults (18+) *</label>
                                                <div class="invalid-feedback">Please select number of adults.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating">
                                                <select class="form-select" id="children">
                                                    <option value="0">No Children</option>
                                                    <option value="1">1 Child</option>
                                                    <option value="2">2 Children</option>
                                                    <option value="3">3 Children</option>
                                                    <option value="4">4+ Children</option>
                                                </select>
                                                <label for="children">Children (Under 18)</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="form-section">
                                    <h5 class="section-title">Additional Information</h5>
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <div class="form-floating">
                                                <textarea class="form-control" placeholder="Special Requests" id="specialRequests" style="height: 120px"></textarea>
                                                <label for="specialRequests">Special Requests or Dietary Requirements</label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="newsletter">
                                                <label class="form-check-label" for="newsletter">
                                                    Subscribe to our newsletter for travel deals and updates
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="terms" required>
                                                <label class="form-check-label" for="terms">
                                                    I agree to the <a href="#" class="text-primary">Terms & Conditions</a> and <a href="#" class="text-primary">Privacy Policy</a> *
                                                </label>
                                                <div class="invalid-feedback">Please accept the terms and conditions.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="form-section">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary btn-lg py-3" type="submit">
                                            <i class="fa fa-paper-plane me-2"></i>Submit Booking Request
                                        </button>
                                        <small class="text-muted text-center">
                                            You will receive a confirmation email within 24 hours
                                        </small>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Enhanced Booking End -->

        <!-- Subscribe Start -->
        <div class="container-fluid subscribe py-5">
            <div class="container text-center py-5">
                <div class="mx-auto text-center" style="max-width: 900px;">
                    <h5 class="subscribe-title px-3">Subscribe</h5>
                    <h1 class="text-white mb-4">Our Newsletter</h1>
                    <p class="text-white mb-5">Lorem ipsum dolor sit amet consectetur adipisicing elit. Laborum tempore nam, architecto doloremque velit explicabo? Voluptate sunt eveniet fuga eligendi! Expedita laudantium fugiat corrupti eum cum repellat a laborum quasi.
                    </p>
                    <div class="position-relative mx-auto">
                        <input class="form-control border-primary rounded-pill w-100 py-3 ps-4 pe-5" type="text" placeholder="Your email">
                        <button type="button" class="btn btn-primary rounded-pill position-absolute top-0 end-0 py-2 px-4 mt-2 me-2">Subscribe</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Subscribe End -->

        <!-- Footer Start -->
        <div class="container-fluid footer py-5">
            <div class="container py-5">
                <div class="row g-5">
                    <div class="col-md-6 col-lg-6 col-xl-3">
                        <div class="footer-item d-flex flex-column">
                            <h4 class="mb-4 text-white">Get In Touch</h4>
                            <a href="https://maps.google.com/?q=123+Street,+New+York,+USA" target="_blank"><i class="fas fa-home me-2"></i> 123 Street, New York, USA</a>
                            <a href="mailto:<EMAIL>"><i class="fas fa-envelope me-2"></i> <EMAIL></a>
                            <a href="tel:+012345678900"><i class="fas fa-phone me-2"></i> +012 345 67890</a>
                            <a href="tel:+012345678900" class="mb-3"><i class="fas fa-print me-2"></i> +012 345 67890</a>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-share fa-2x text-white me-2"></i>
                                <a class="btn-square btn btn-primary rounded-circle mx-1" href="https://facebook.com/travela" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <a class="btn-square btn btn-primary rounded-circle mx-1" href="https://twitter.com/travela" target="_blank"><i class="fab fa-twitter"></i></a>
                                <a class="btn-square btn btn-primary rounded-circle mx-1" href="https://instagram.com/travela" target="_blank"><i class="fab fa-instagram"></i></a>
                                <a class="btn-square btn btn-primary rounded-circle mx-1" href="https://linkedin.com/company/travela" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-6 col-xl-3">
                        <div class="footer-item d-flex flex-column">
                            <h4 class="mb-4 text-white">Company</h4>
                            <a href="about.html"><i class="fas fa-angle-right me-2"></i> About</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Careers</a>
                            <a href="blog.html"><i class="fas fa-angle-right me-2"></i> Blog</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Press</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Gift Cards</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Magazine</a>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-6 col-xl-3">
                        <div class="footer-item d-flex flex-column">
                            <h4 class="mb-4 text-white">Support</h4>
                            <a href="contact.html"><i class="fas fa-angle-right me-2"></i> Contact</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Legal Notice</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Privacy Policy</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Terms and Conditions</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Sitemap</a>
                            <a href="#"><i class="fas fa-angle-right me-2"></i> Cookie policy</a>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-6 col-xl-3">
                        <div class="footer-item">
                            <div class="row gy-3 gx-2 mb-4">
                                <div class="col-xl-6">
                                    <form>
                                        <div class="form-floating">
                                            <select class="form-select bg-dark border" id="select1">
                                                <option value="1">Arabic</option>
                                                <option value="2">German</option>
                                                <option value="3">Greek</option>
                                                <option value="3">New York</option>
                                            </select>
                                            <label for="select1">English</label>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-xl-6">
                                    <form>
                                        <div class="form-floating">
                                            <select class="form-select bg-dark border" id="select1">
                                                <option value="1">USD</option>
                                                <option value="2">EUR</option>
                                                <option value="3">INR</option>
                                                <option value="3">GBP</option>
                                            </select>
                                            <label for="select1">$</label>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <h4 class="text-white mb-3">Payments</h4>
                            <div class="footer-bank-card">
                                <a href="#" class="text-white me-2"><i class="fab fa-cc-amex fa-2x"></i></a>
                                <a href="#" class="text-white me-2"><i class="fab fa-cc-visa fa-2x"></i></a>
                                <a href="#" class="text-white me-2"><i class="fas fa-credit-card fa-2x"></i></a>
                                <a href="#" class="text-white me-2"><i class="fab fa-cc-mastercard fa-2x"></i></a>
                                <a href="#" class="text-white me-2"><i class="fab fa-cc-paypal fa-2x"></i></a>
                                <a href="#" class="text-white"><i class="fab fa-cc-discover fa-2x"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer End -->
        
        <!-- Copyright Start -->
        <div class="container-fluid copyright text-body py-4">
            <div class="container">
                <div class="row g-4 align-items-center">
                    <div class="col-md-6 text-center text-md-end mb-md-0">
                        <i class="fas fa-copyright me-2"></i><a class="text-white" href="#">Your Site Name</a>, All right reserved.
                    </div>
                    <div class="col-md-6 text-center text-md-start">
                        <!--/*** This template is free as long as you keep the below author’s credit link/attribution link/backlink. ***/-->
                        <!--/*** If you'd like to use the template without the below author’s credit link/attribution link/backlink, ***/-->
                        <!--/*** you can purchase the Credit Removal License from "https://htmlcodex.com/credit-removal". ***/-->
                        Designed By <a class="text-white" href="https://htmlcodex.com">HTML Codex</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Copyright End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-primary btn-primary-outline-0 btn-md-square back-to-top"><i class="fa fa-arrow-up"></i></a>   

        
        <!-- JavaScript Libraries -->
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
        <script src="lib/easing/easing.min.js"></script>
        <script src="lib/waypoints/waypoints.min.js"></script>
        <script src="lib/owlcarousel/owl.carousel.min.js"></script>
        <script src="lib/lightbox/js/lightbox.min.js"></script>
        

        <!-- Template Javascript -->
        <script src="js/main.js"></script>

        <!-- Backend Integration -->
        <script src="js/backend.js"></script>

        <!-- Booking Form Script -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('bookingForm');
            const dateInput = document.getElementById('datetime');

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            dateInput.setAttribute('min', today);

            // Pre-fill form from URL parameters (from quick booking widget)
            const urlParams = new URLSearchParams(window.location.search);

            // Pre-fill destination
            const destination = urlParams.get('destination');
            if (destination) {
                const destinationSelect = document.getElementById('select1');
                destinationSelect.value = destination;
            }

            // Pre-fill date
            const date = urlParams.get('date');
            if (date) {
                dateInput.value = date;
            }

            // Pre-fill guests
            const guests = urlParams.get('guests');
            if (guests) {
                const guestsSelect = document.getElementById('SelectPerson');
                guestsSelect.value = guests;
            }

            // Show welcome message if coming from quick booking
            if (destination || date || guests) {
                const welcomeDiv = document.createElement('div');
                welcomeDiv.className = 'alert alert-info alert-dismissible fade show mb-4';
                welcomeDiv.innerHTML = `
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>Great choice!</strong> We've pre-filled your booking details. Please review and complete the form below.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                form.parentNode.insertBefore(welcomeDiv, form);

                // Auto-hide after 5 seconds
                setTimeout(function() {
                    if (welcomeDiv.parentNode) {
                        welcomeDiv.remove();
                    }
                }, 5000);
            }

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Remove previous validation classes
                form.classList.remove('was-validated');

                // Check form validity
                if (form.checkValidity()) {
                    // Form is valid, show success message
                    const button = form.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;

                    // Show loading state
                    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Processing...';
                    button.disabled = true;

                    // Simulate booking processing (replace with actual backend call)
                    setTimeout(function() {
                        // Show success message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
                        alertDiv.innerHTML = `
                            <strong>Booking Successful!</strong> Your tour booking has been submitted successfully.
                            You'll receive a confirmation email shortly with booking details and payment instructions.
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;

                        form.parentNode.insertBefore(alertDiv, form);

                        // Reset form
                        form.reset();
                        form.classList.remove('was-validated');

                        // Reset button
                        button.innerHTML = originalText;
                        button.disabled = false;

                        // Auto-hide success message after 8 seconds
                        setTimeout(function() {
                            if (alertDiv.parentNode) {
                                alertDiv.remove();
                            }
                        }, 8000);

                    }, 3000); // Simulate 3 second processing delay
                } else {
                    // Form is invalid, show validation errors
                    form.classList.add('was-validated');
                }
            });
        });
        </script>
    </body>

</html>